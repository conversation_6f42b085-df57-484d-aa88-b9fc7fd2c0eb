using UnityEngine;

[CreateAssetMenu(fileName = "RopeStats", menuName = "Anchor-Light/Rope Stats", order = 1)]
public class RopeStats : ScriptableObject
{
    [Header("ROPE")]
    [Tooltip("The speed at which the player climbs the rope.")]
    public float ClimbSpeed = 5f;

    [Tooltip("The force applied to the player when they jump off the rope.")]
    public Vector2 RopeJumpPower = new Vector2(5, 10);

    [Tooltip("The cooldown period for the rope after a player has used it.")]
    public float RopeCooldown = 1f;
}
