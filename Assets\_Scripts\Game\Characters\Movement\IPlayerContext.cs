using UnityEngine;

/// <summary>
/// Interface providing context about the player's current state.
/// Used by movement components to make decisions without tight coupling.
/// </summary>
public interface IPlayerContext
{
    // Physics components
    Rigidbody2D Rigidbody { get; }
    Collider2D Collider { get; }

    // State queries
    bool IsGrounded { get; }
    bool IsSwimming { get; }
    bool IsWallClimbing { get; }
    bool IsRopeClimbing { get; }
    bool IsDashing { get; }
    bool IsGliding { get; }

    // Environmental context
    Collider2D WaterCollider { get; }
    float LastFacing { get; }

    // Utility methods
    void SetGrounded(bool grounded);
    void SetSwimming(bool swimming, Collider2D waterCollider = null);
    void SetWallClimbing(bool wallClimbing);
    void SetGliding(bool gliding);
    void SetDashing(bool dashing);
}
