using UnityEngine;

/// <summary>
/// Configuration data for wall climbing mechanics, extracted from monolithic ScriptableStats.
/// </summary>
[CreateAssetMenu(
    fileName = "WallClimbingStats",
    menuName = "Anchor-Light/Wall Climbing Stats",
    order = 1
)]
public class WallClimbingStats : ScriptableObject
{
    [Header("WALL CLIMBING")]
    [Tooltip("How long the player can grip a wall before sliding")]
    public float WallGripDuration = 1f;

    [Tooltip("Speed at which player slides down wall after grip expires")]
    public float WallSlideSpeed = 2f;

    [Tooltip("Distance to check for walls")]
    public float WallCheckDistance = 0.6f;

    [Tooltip("Force applied when jumping off a wall")]
    public Vector2 WallJumpForce = new Vector2(15, 25);

    [Tooltip("Layer mask for wall detection")]
    public LayerMask WallLayer = -1;
}
