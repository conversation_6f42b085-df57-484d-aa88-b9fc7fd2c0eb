using UnityEditor;
using UnityEngine;

[CustomEditor(typeof(Rope))]
public class RopeGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        Rope ropeGenerator = (Rope)target;

        if (GUILayout.Button("Generate Rope"))
        {
            if (!Application.isPlaying)
            {
                // Call the GenerateRope method
                ropeGenerator.GenerateRope();
                // Mark scene dirty so changes are saved
                EditorUtility.SetDirty(ropeGenerator);
                UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(
                    ropeGenerator.gameObject.scene
                );
            }
            else
            {
                Debug.LogWarning("Generate Rope button works only in edit mode.");
            }
        }
    }
}
