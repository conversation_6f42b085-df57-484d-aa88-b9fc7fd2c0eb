/// <summary>
/// Base interface for all states in the state machine.
/// Provides clear lifecycle methods for state management.
/// </summary>
public interface IState
{
    /// <summary>
    /// Called when entering this state
    /// </summary>
    void Enter();

    /// <summary>
    /// Called every frame while in this state
    /// </summary>
    void Update();

    /// <summary>
    /// Called every fixed frame while in this state
    /// </summary>
    void FixedUpdate();

    /// <summary>
    /// Called when exiting this state
    /// </summary>
    void Exit();

    /// <summary>
    /// Check if this state can transition to another state
    /// </summary>
    bool CanTransitionTo(IState newState);
}
