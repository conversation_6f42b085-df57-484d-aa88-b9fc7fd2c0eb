using UnityEngine;

/// <summary>
/// Generic state machine implementation.
/// Manages state transitions and ensures proper lifecycle calls.
/// </summary>
public class StateMachine
{
    private IState _currentState;
    private IState _previousState;

    public IState CurrentState => _currentState;
    public IState PreviousState => _previousState;

    /// <summary>
    /// Initialize the state machine with a starting state
    /// </summary>
    public void Initialize(IState startingState)
    {
        _currentState = startingState;
        _currentState?.Enter();
    }

    /// <summary>
    /// Attempt to transition to a new state
    /// </summary>
    public bool TryTransitionTo(IState newState)
    {
        if (newState == null)
        {
            Debug.LogWarning("Attempted to transition to null state");
            return false;
        }

        if (_currentState == newState)
        {
            return false; // Already in this state
        }

        if (_currentState != null && !_currentState.CanTransitionTo(newState))
        {
            return false; // Current state doesn't allow this transition
        }

        // Perform the transition
        _previousState = _currentState;
        _currentState?.Exit();
        _currentState = newState;
        _currentState.Enter();

        return true;
    }

    /// <summary>
    /// Force a transition without checking CanTransitionTo
    /// </summary>
    public void ForceTransitionTo(IState newState)
    {
        if (newState == null)
        {
            Debug.LogWarning("Attempted to force transition to null state");
            return;
        }

        _previousState = _currentState;
        _currentState?.Exit();
        _currentState = newState;
        _currentState.Enter();
    }

    /// <summary>
    /// Update the current state
    /// </summary>
    public void Update()
    {
        _currentState?.Update();
    }

    /// <summary>
    /// Fixed update the current state
    /// </summary>
    public void FixedUpdate()
    {
        _currentState?.FixedUpdate();
    }
}
