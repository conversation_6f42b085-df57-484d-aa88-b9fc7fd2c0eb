using UnityEngine;

namespace AnchorLight.Game.Characters
{
    /// <summary>
    /// VERY primitive animator example.
    /// </summary>
    public class PlayerAnimator : MonoBehaviour
    {
        [Header("References")]
        [SerializeField]
        private Animator _anim;

        [SerializeField]
        private Sprite<PERSON><PERSON><PERSON> _sprite;

        [Header("Settings")]
        [SerializeField, Range(1f, 3f)]
        private float _maxIdleSpeed = 2;

        [SerializeField]
        private float _maxTilt = 5;

        [SerializeField]
        private float _tiltSpeed = 20;

        [Header("Audio Clips")]
        [SerializeField]
        private AudioClip[] _footsteps;

        private AudioSource _source;
        private IPlayerController _player;
        private bool _grounded;

        private void Awake()
        {
            _source = GetComponent<AudioSource>();
            _player = GetComponentInParent<IPlayerController>();
        }

        private void OnEnable()
        {
            _player.Jumped += OnJumped;
            _player.GroundedChanged += OnGroundedChanged;
        }

        private void OnDisable()
        {
            _player.Jumped -= OnJumped;
            _player.GroundedChanged -= OnGroundedChanged;
        }

        private void Update()
        {
            if (_player == null)
                return;

            // Wall climbing state
            if (_player.IsWallClimbing)
            {
                _anim.SetBool(WallClimbingKey, true);
                _anim.SetBool(SwimmingKey, false);
                _anim.SetBool(DivingKey, false);
                _anim.SetBool(RunningKey, false);
                return;
            }
            else
            {
                _anim.SetBool(WallClimbingKey, false);
            }

            // Swimming orientation - tilt head towards swim input when in water
            if (_player.IsSwimming)
            {
                _anim.SetBool(SwimmingKey, true);
                _anim.SetBool(RunningKey, false);
                if (_player.IsDiving)
                {
                    _anim.SetBool(DivingKey, true);
                }
                else
                {
                    _anim.SetBool(DivingKey, false);
                }
                var swimInput = _player.FrameInput;
                if (swimInput != Vector2.zero)
                {
                    // flip sprite based on horizontal swim direction
                    _sprite.flipX = swimInput.x < 0;
                }
                return;
            }
            else
            {
                _anim.SetBool(DivingKey, false);
                _anim.SetBool(SwimmingKey, false);
            }

            if (_player.FrameInput.x != 0)
                _sprite.flipX = _player.FrameInput.x < 0;

            HandleRunningAnimation();
            HandleIdleSpeed();
            HandleCharacterTilt();
        }

        private void HandleRunningAnimation()
        {
            bool isMoving = _grounded && Mathf.Abs(_player.FrameInput.x) > 0.1f;

            // Set running animation state
            _anim.SetBool(RunningKey, isMoving);
        }

        private void HandleIdleSpeed()
        {
            var inputStrength = Mathf.Abs(_player.FrameInput.x);
            _anim.SetFloat(IdleSpeedKey, Mathf.Lerp(1, _maxIdleSpeed, inputStrength));
        }

        private void HandleCharacterTilt()
        {
            // Only apply this tilt logic when grounded
            if (_grounded)
            {
                var runningTilt = Quaternion.Euler(0, 0, _maxTilt * _player.FrameInput.x);
                _anim.transform.up = Vector3.RotateTowards(
                    _anim.transform.up,
                    runningTilt * Vector2.up,
                    _tiltSpeed * Time.deltaTime,
                    0f
                );
            }
            // When not grounded, do nothing, allowing other scripts to control rotation.
        }

        private void OnJumped()
        {
            _anim.SetTrigger(JumpKey);
            _anim.ResetTrigger(GroundedKey);
        }

        private void OnGroundedChanged(bool grounded, float impact)
        {
            _grounded = grounded;

            if (grounded)
            {
                _anim.SetTrigger(GroundedKey);
                _source.PlayOneShot(_footsteps[Random.Range(0, _footsteps.Length)]);
            }
        }

        private static readonly int GroundedKey = Animator.StringToHash("Grounded");
        private static readonly int IdleSpeedKey = Animator.StringToHash("IdleSpeed");
        private static readonly int JumpKey = Animator.StringToHash("Jump");
        private static readonly int WallClimbingKey = Animator.StringToHash("WallClimbing");
        private static readonly int RunningKey = Animator.StringToHash("Running");
        private static readonly int SwimmingKey = Animator.StringToHash("Swimming");
        private static readonly int DivingKey = Animator.StringToHash("Diving");
    }
}
