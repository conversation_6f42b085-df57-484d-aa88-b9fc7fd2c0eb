using UnityEngine;

[RequireComponent(typeof(Rigidbody2D))]
public class Rope : MonoBehaviour
{
    [Head<PERSON>("Behavior")]
    [Tooltip("Direction the player is allowed to face on the rope")]
    public RopeDirection Direction = RopeDirection.Both;

    [Header("Rope Settings")]
    [Tooltip("Prefab of a single rope segment with Rigidbody2D and HingeJoint2D")]
    public GameObject ropeSegmentPrefab;

    [Toolt<PERSON>("Anchor Rigidbody2D to connect the topmost segment's hinge")]
    public Rigidbody2D anchor;

    [Tooltip("Desired total rope length in units")]
    public float ropeLength = 5f;

    [Tooltip("Scale to apply uniformly to all rope segments")]
    public Vector3 segmentScale = Vector3.one;

    [Tooltip("Flip the x-axis of each subsequent segment")]
    public bool flipSubsequent = false;

    [Tooltip("Randomize the flip of each segment")]
    public bool randomizeFlip = false;

    [Header("Hinge Joint Limits")]
    public float hingeLowerLimit = -60f; // Adjusted for smoother swinging
    public float hingeUpperLimit = 60f;

    public GameObject[] Segments { get; private set; }
    public int ConnectedSegmentIndex => _connectedSegmentIndex;
    public bool IsPlayerConnected => _connectedBody != null;

    private Rigidbody2D _connectedBody;
    private Joint2D _playerJoint; // Changed to base Joint2D to support different joint types
    private int _connectedSegmentIndex = -1;

    // Smooth climbing variables
    private bool _isClimbing = false;
    private float _climbProgress = 0f;
    private int _targetSegmentIndex = -1;
    private Vector3 _climbStartPosition;
    private Vector3 _climbTargetPosition;

    void Start()
    {
        if (ropeSegmentPrefab == null)
        {
            Debug.LogError("Rope segment prefab is not assigned.");
            return;
        }
        if (anchor == null)
        {
            Debug.LogError("Anchor Rigidbody2D is not assigned.");
            return;
        }

        if (transform.childCount == 0)
        {
            GenerateRope();
        }
        else
        {
            Segments = new GameObject[transform.childCount];
            for (int i = 0; i < transform.childCount; i++)
            {
                Segments[i] = transform.GetChild(i).gameObject;
            }
        }
    }

    public void GenerateRope()
    {
        if (Segments != null)
        {
            foreach (var seg in Segments)
            {
                if (seg != null)
                {
                    if (Application.isEditor && !Application.isPlaying)
                        DestroyImmediate(seg);
                    else
                        Destroy(seg);
                }
            }
        }

        Vector2 segmentLength = GetSegmentLength();
        if (segmentLength.y <= 0)
        {
            Debug.LogError(
                "Invalid segment length. Make sure prefab has a Renderer or Collider with size."
            );
            return;
        }

        int segmentCount = Mathf.CeilToInt(ropeLength / segmentLength.y);
        Segments = new GameObject[segmentCount];

        Rigidbody2D prevRigidbody = anchor;
        Vector3 currentPos = transform.position;

        for (int i = 0; i < segmentCount; i++)
        {
            GameObject segment = Instantiate(ropeSegmentPrefab, transform);
            segment.name = "RopeSegment_" + i;

            Vector3 currentScale = segmentScale;
            if (flipSubsequent)
            {
                if (randomizeFlip)
                {
                    if (Random.value > 0.5f)
                    {
                        currentScale.x *= -1;
                    }
                }
                else
                {
                    if (i % 2 != 0)
                    {
                        currentScale.x *= -1;
                    }
                }
            }
            segment.transform.localScale = currentScale;

            // Position segments to connect properly without gaps
            if (i == 0)
            {
                // First segment starts at rope position, offset down by half segment height
                segment.transform.position = currentPos + Vector3.down * (segmentLength.y * 0.5f);
            }
            else
            {
                // Subsequent segments positioned to connect at their top edge to previous segment's bottom edge
                Vector3 prevSegmentPos = Segments[i - 1].transform.position;
                segment.transform.position = prevSegmentPos + Vector3.down * segmentLength.y;
            }

            var segmentCollider = segment.GetComponent<Collider2D>();
            if (segmentCollider != null)
            {
                segmentCollider.isTrigger = true;
            }
            var prevSegmentCollider = prevRigidbody.GetComponent<Collider2D>();
            if (segmentCollider != null && prevSegmentCollider != null && prevRigidbody != anchor)
            {
                Physics2D.IgnoreCollision(segmentCollider, prevSegmentCollider);
            }

            Rigidbody2D rb = segment.GetComponent<Rigidbody2D>();
            if (rb == null)
            {
                Debug.LogError("Rope segment prefab is missing Rigidbody2D.");
                if (Application.isEditor && !Application.isPlaying)
                    DestroyImmediate(segment);
                else
                    Destroy(segment);
                return;
            }

            // Configure rigidbody for realistic rope physics
            rb.mass = 0.1f; // Light segments for realistic swinging
            rb.linearDamping = 0.1f; // Low damping for natural swing
            rb.angularDamping = 0.5f; // Some angular damping to prevent wild spinning
            rb.gravityScale = 1f; // Full gravity for natural hang

            HingeJoint2D hinge = segment.GetComponent<HingeJoint2D>();
            if (hinge == null)
            {
                hinge = segment.AddComponent<HingeJoint2D>();
            }

            hinge.connectedBody = prevRigidbody;
            hinge.autoConfigureConnectedAnchor = false;

            // Set anchor points to connect segments properly
            hinge.anchor = new Vector2(0, segmentLength.y * 0.5f); // Top of current segment

            if (prevRigidbody == anchor)
            {
                // For anchor connection, use the anchor's position
                hinge.connectedAnchor = Vector2.zero;
            }
            else
            {
                // For segment-to-segment connection, connect to bottom of previous segment
                hinge.connectedAnchor = new Vector2(0, -segmentLength.y * 0.5f);
            }

            // More realistic hinge limits for rope movement
            JointAngleLimits2D limits = new JointAngleLimits2D
            {
                min = hingeLowerLimit, // Use configurable limits
                max = hingeUpperLimit,
            };
            hinge.limits = limits;
            hinge.useLimits = true;
            hinge.enableCollision = false;

            // Remove motor - it dampens natural rope physics
            hinge.useMotor = false;

            prevRigidbody = rb;
            Segments[i] = segment;
        }
    }

    private Vector2 GetSegmentLength()
    {
        if (ropeSegmentPrefab == null)
        {
            Debug.LogWarning("ropeSegmentPrefab is not assigned.");
            return Vector2.zero;
        }

        var spriteRenderer = ropeSegmentPrefab.GetComponent<SpriteRenderer>();
        if (spriteRenderer != null && spriteRenderer.sprite != null)
        {
            Vector3 size = spriteRenderer.bounds.size;
            return new Vector2(size.x, size.y);
        }

        var collider = ropeSegmentPrefab.GetComponent<Collider2D>();
        if (collider != null)
        {
            Vector3 size = collider.bounds.size;
            return new Vector2(size.x, size.y);
        }

        Debug.LogWarning("No SpriteRenderer or Collider2D found on ropeSegmentPrefab.");
        return Vector2.zero;
    }

    public void Attach(Rigidbody2D playerBody, int segmentIndex)
    {
        if (
            _connectedBody != null
            || Segments == null
            || segmentIndex < 0
            || segmentIndex >= Segments.Length
        )
        {
            return;
        }

        _connectedBody = playerBody;
        _connectedSegmentIndex = segmentIndex;

        GameObject segment = Segments[_connectedSegmentIndex];

        // Remove any existing joints to avoid conflicts
        var existingJoints = playerBody.GetComponents<Joint2D>();
        foreach (var joint in existingJoints)
        {
            if (joint != null)
                Destroy(joint);
        }

        // Use DistanceJoint2D for realistic rope physics instead of HingeJoint2D
        DistanceJoint2D playerJoint = playerBody.gameObject.AddComponent<DistanceJoint2D>();
        _playerJoint = playerJoint;

        playerJoint.connectedBody = segment.GetComponent<Rigidbody2D>();
        playerJoint.autoConfigureDistance = false;

        // Calculate distance from player's hand anchor to segment center
        Vector3 handAnchorWorld = playerBody.GetComponent<PlayerController>().handAnchor.position;
        Vector3 segmentWorld = segment.transform.position;
        float ropeDistance = Vector3.Distance(handAnchorWorld, segmentWorld);

        // Ensure minimum rope distance to prevent too-tight constraints
        ropeDistance = Mathf.Max(ropeDistance, 0.5f);

        playerJoint.distance = ropeDistance;
        playerJoint.maxDistanceOnly = false; // FIXED: Keep exact distance for proper rope constraint
        playerJoint.enableCollision = false;

        // Set anchor points
        Vector3 handAnchorLocal = playerBody.transform.InverseTransformPoint(handAnchorWorld);
        playerJoint.anchor = handAnchorLocal;
        playerJoint.connectedAnchor = Vector2.zero; // Center of segment

        // Reduce player's drag to allow better swinging
        playerBody.linearDamping = 0.5f;
        playerBody.angularDamping = 0.5f;

        // Ensure player mass is reasonable for rope physics
        if (playerBody.mass > 5f)
        {
            playerBody.mass = 2f; // Reasonable mass for rope physics
        }
    }

    public void Detach()
    {
        if (_connectedBody != null && _playerJoint != null)
        {
            // Store current velocity before detaching for momentum preservation
            Vector2 velocity = _connectedBody.linearVelocity;

            // Restore player's original physics properties
            _connectedBody.linearDamping = 1f; // Restore normal drag
            _connectedBody.angularDamping = 1f;

            // Destroy the joint
            Destroy(_playerJoint);

            // Apply momentum in the direction of swing for realistic detachment
            Vector2 swingDirection = velocity.normalized;
            float swingMagnitude = velocity.magnitude;
            _connectedBody.linearVelocity = swingDirection * Mathf.Min(swingMagnitude * 1.2f, 15f);

            _connectedBody = null;
            _playerJoint = null;
            _connectedSegmentIndex = -1;
        }
    }

    public void Climb(float direction)
    {
        if (_connectedBody == null || _isClimbing)
            return;

        int newSegmentIndex = _connectedSegmentIndex + (direction > 0 ? -1 : 1);
        if (newSegmentIndex >= 0 && newSegmentIndex < Segments.Length)
        {
            // Start smooth climbing transition
            _isClimbing = true;
            _climbProgress = 0f;
            _targetSegmentIndex = newSegmentIndex;
            _climbStartPosition = _connectedBody.transform.position;
            _climbTargetPosition = Segments[newSegmentIndex].transform.position;

            // Adjust target position to maintain proper distance from rope segment
            Vector3 currentOffset =
                _connectedBody.transform.position
                - Segments[_connectedSegmentIndex].transform.position;
            _climbTargetPosition += currentOffset;
        }
    }

    public void UpdateClimbing()
    {
        if (!_isClimbing || _connectedBody == null)
            return;

        // Smooth interpolation speed - adjust this value to control climb speed
        float climbSpeed = 3f; // Units per second
        _climbProgress += climbSpeed * Time.fixedDeltaTime;

        if (_climbProgress >= 1f)
        {
            // Climbing complete - snap to final position and update connection
            _climbProgress = 1f;
            _isClimbing = false;

            // Update the distance joint to connect to new segment
            if (_playerJoint is DistanceJoint2D distanceJoint)
            {
                GameObject newSegment = Segments[_targetSegmentIndex];
                distanceJoint.connectedBody = newSegment.GetComponent<Rigidbody2D>();

                // Recalculate distance to new segment
                Vector3 handAnchorWorld = _connectedBody
                    .GetComponent<PlayerController>()
                    .handAnchor.position;
                Vector3 segmentWorld = newSegment.transform.position;
                float newDistance = Vector3.Distance(handAnchorWorld, segmentWorld);
                newDistance = Mathf.Max(newDistance, 0.5f); // Ensure minimum distance
                distanceJoint.distance = newDistance;
            }

            _connectedSegmentIndex = _targetSegmentIndex;
        }
        else
        {
            // Smooth interpolation between start and target positions
            Vector3 currentTargetPos = Vector3.Lerp(
                _climbStartPosition,
                _climbTargetPosition,
                _climbProgress
            );

            // Apply the movement by adjusting velocity towards target
            Vector3 currentPos = _connectedBody.transform.position;
            Vector3 moveDirection = (currentTargetPos - currentPos).normalized;
            float moveDistance = Vector3.Distance(currentPos, currentTargetPos);

            // Apply movement force to smoothly move player
            if (moveDistance > 0.1f)
            {
                Vector2 climbForce = moveDirection * Mathf.Min(moveDistance * 10f, 8f);
                _connectedBody.AddForce(climbForce, ForceMode2D.Force);
            }
        }
    }

    public void Swing(float swingInput)
    {
        if (
            _connectedBody == null
            || _connectedSegmentIndex < 0
            || _connectedSegmentIndex >= Segments.Length
        )
            return;

        // Get the rope segment the player is connected to
        var segmentRb = Segments[_connectedSegmentIndex].GetComponent<Rigidbody2D>();
        if (segmentRb == null)
            return;

        // Calculate pendulum force based on rope direction from anchor point
        Vector3 playerPos = _connectedBody.transform.position;
        Vector3 segmentPos = segmentRb.transform.position;

        // Calculate the direction from segment to player (rope direction)
        Vector3 ropeDirection = (playerPos - segmentPos).normalized;

        // Calculate perpendicular direction for swinging (tangent to the arc)
        Vector3 swingDirection = new Vector3(-ropeDirection.y, ropeDirection.x, 0).normalized;

        // Apply swing input to determine direction
        Vector2 finalSwingDirection = swingDirection * swingInput;

        // Use a stronger force for more responsive swinging
        float swingForce = 25f; // Increased from 15f
        Vector2 appliedForce = finalSwingDirection * swingForce;

        // Apply force to player for immediate response
        _connectedBody.AddForce(appliedForce, ForceMode2D.Force);

        // Apply smaller counter-force to the connected segment for realistic rope physics
        segmentRb.AddForce(-appliedForce * 0.2f, ForceMode2D.Force);

        // Also apply force to segments above to create wave effect
        for (int i = Mathf.Max(0, _connectedSegmentIndex - 2); i < _connectedSegmentIndex; i++)
        {
            var upperSegmentRb = Segments[i].GetComponent<Rigidbody2D>();
            if (upperSegmentRb != null)
            {
                float dampening = 0.1f * (1f - (float)(_connectedSegmentIndex - i) / 3f);
                upperSegmentRb.AddForce(-appliedForce * dampening, ForceMode2D.Force);
            }
        }
    }

    public int GetClosestSegmentIndex(Vector3 position)
    {
        if (Segments == null || Segments.Length == 0)
        {
            return -1;
        }

        int closestIndex = 0;
        float minDistance = Vector3.Distance(position, Segments[0].transform.position);
        for (int i = 1; i < Segments.Length; i++)
        {
            float distance = Vector3.Distance(position, Segments[i].transform.position);
            if (distance < minDistance)
            {
                minDistance = distance;
                closestIndex = i;
            }
        }
        return closestIndex;
    }

#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (Segments == null)
            return;
        Gizmos.color = Color.cyan;
        for (int i = 0; i < Segments.Length; i++)
        {
            if (Segments[i] != null)
            {
                Gizmos.DrawWireSphere(Segments[i].transform.position, 0.2f);
                if (i > 0)
                {
                    Gizmos.DrawLine(
                        Segments[i - 1].transform.position,
                        Segments[i].transform.position
                    );
                }
            }
        }
    }
#endif
}
