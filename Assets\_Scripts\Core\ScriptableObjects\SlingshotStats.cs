using UnityEngine;

namespace AnchorLight.Core.ScriptableObjects
{
    [CreateAssetMenu(menuName = "Weapons/Slingshot Stats")]
    public class SlingshotStats : ScriptableObject
    {
        [Tooltip("Maximum time the slingshot can be charged")]
        public float MaxChargeTime = 1f;
        [Tooltip("Maximum pull strength (fraction of full) when fully charged")]
        public float MaxPullStrength = 1f;
        [Tooltip("Base shot speed multiplier")]
        public float ShotSpeed = 15f;
        [Tooltip("Number of projectiles to pool at startup")]
        public int PoolSize = 10;
    }
}