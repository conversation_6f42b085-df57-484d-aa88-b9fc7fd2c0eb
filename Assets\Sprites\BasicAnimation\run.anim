%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: run
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - serializedVersion: 2
    curve:
    - time: 0
      value: {fileID: 6515904674758421358, guid: 3567934016f604c15a1df44c75be725d, type: 3}
    - time: 0.05
      value: {fileID: -3486833207814245543, guid: e44936a140bc74500b2fd3fb35bcaa55, type: 3}
    - time: 0.13333334
      value: {fileID: 1722970863098500168, guid: c44ed635eca1948658dc1b83319624b3, type: 3}
    - time: 0.23333333
      value: {fileID: -4259635432228036503, guid: 3b5a13701f05e8720a861e19e3183c9b, type: 3}
    - time: 0.3
      value: {fileID: 8304762679976087527, guid: 887b5533bc069066282a53cfb460a595, type: 3}
    - time: 0.4
      value: {fileID: 8382007410915793689, guid: f131d27c90c3f0ae8b2bf7bb2039e923, type: 3}
    - time: 0.48333332
      value: {fileID: -752210870200769308, guid: 5abc48d986348fe0dbcd2584630529e8, type: 3}
    - time: 0.53333336
      value: {fileID: -8398269420452314738, guid: 2ab70f5377d9fd9f58106b725e6f9bb3, type: 3}
    attribute: m_Sprite
    path: 
    classID: 212
    script: {fileID: 0}
    flags: 2
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping:
    - {fileID: 6515904674758421358, guid: 3567934016f604c15a1df44c75be725d, type: 3}
    - {fileID: -3486833207814245543, guid: e44936a140bc74500b2fd3fb35bcaa55, type: 3}
    - {fileID: 1722970863098500168, guid: c44ed635eca1948658dc1b83319624b3, type: 3}
    - {fileID: -4259635432228036503, guid: 3b5a13701f05e8720a861e19e3183c9b, type: 3}
    - {fileID: 8304762679976087527, guid: 887b5533bc069066282a53cfb460a595, type: 3}
    - {fileID: 8382007410915793689, guid: f131d27c90c3f0ae8b2bf7bb2039e923, type: 3}
    - {fileID: -752210870200769308, guid: 5abc48d986348fe0dbcd2584630529e8, type: 3}
    - {fileID: -8398269420452314738, guid: 2ab70f5377d9fd9f58106b725e6f9bb3, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.55
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
