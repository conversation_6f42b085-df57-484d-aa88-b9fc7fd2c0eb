using UnityEngine;

/// <summary>
/// Configuration data for dash ability, extracted from monolithic ScriptableStats.
/// </summary>
[CreateAssetMenu(fileName = "DashStats", menuName = "Anchor-Light/Dash Stats", order = 1)]
public class DashStats : ScriptableObject
{
    [Header("DASH")]
    [Tooltip("The speed applied during dash")]
    public float DashPower = 50f;

    [Tooltip("The duration of the dash in seconds")]
    public float DashDuration = 0.2f;

    [Tooltip("The maximum dash distance")]
    public float DashDistance = 3f;
}
