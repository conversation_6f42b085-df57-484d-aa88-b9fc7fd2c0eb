using NUnit.Framework;

/// <summary>
/// Unit tests for the ServiceLocator to ensure proper dependency injection.
/// </summary>
public class ServiceLocatorTests
{
    // Mock service for testing
    private class MockInputService : IInputService
    {
        public FrameInput CurrentInput { get; private set; }
        public float LastFacing { get; private set; }

        public void UpdateInput() { }

        public void ConsumeJump() { }

        public void ConsumeDash() { }
    }

    [SetUp]
    public void Setup()
    {
        ServiceLocator.Clear();
    }

    [Test]
    public void ServiceLocator_RegisterAndGet_WorksCorrectly()
    {
        // Arrange
        var mockService = new MockInputService();

        // Act
        ServiceLocator.Register<IInputService>(mockService);
        var retrievedService = ServiceLocator.Get<IInputService>();

        // Assert
        Assert.IsNotNull(retrievedService, "Should retrieve registered service");
        Assert.AreSame(mockService, retrievedService, "Should return the same instance");
    }

    [Test]
    public void ServiceLocator_GetUnregisteredService_ReturnsNull()
    {
        // Act
        var service = ServiceLocator.Get<IInputService>();

        // Assert
        Assert.IsNull(service, "Should return null for unregistered service");
    }

    [Test]
    public void ServiceLocator_IsRegistered_WorksCorrectly()
    {
        // Arrange
        var mockService = new MockInputService();

        // Act & Assert
        Assert.IsFalse(
            ServiceLocator.IsRegistered<IInputService>(),
            "Should not be registered initially"
        );

        ServiceLocator.Register<IInputService>(mockService);
        Assert.IsTrue(
            ServiceLocator.IsRegistered<IInputService>(),
            "Should be registered after registration"
        );
    }

    [Test]
    public void ServiceLocator_Clear_RemovesAllServices()
    {
        // Arrange
        var mockService = new MockInputService();
        ServiceLocator.Register<IInputService>(mockService);

        // Act
        ServiceLocator.Clear();

        // Assert
        Assert.IsFalse(
            ServiceLocator.IsRegistered<IInputService>(),
            "Should not be registered after clear"
        );
        Assert.IsFalse(ServiceLocator.IsInitialized, "Should not be initialized after clear");
    }

    [TearDown]
    public void TearDown()
    {
        ServiceLocator.Clear();
    }
}
