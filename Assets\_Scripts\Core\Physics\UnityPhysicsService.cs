using UnityEngine;

/// <summary>
/// Unity-specific implementation of physics service.
/// Encapsulates all Unity Physics2D calls for easier testing and mocking.
/// </summary>
public class UnityPhysicsService : IPhysicsService
{
    private bool _cachedQueryStartInColliders;

    public UnityPhysicsService()
    {
        _cachedQueryStartInColliders = Physics2D.queriesStartInColliders;
    }

    public bool IsGrounded(Collider2D collider, LayerMask groundLayers, float distance)
    {
        SetupPhysicsQuery();

        var hit = Physics2D.CapsuleCast(
            collider.bounds.center,
            collider.bounds.size,
            CapsuleDirection2D.Vertical,
            0,
            Vector2.down,
            distance,
            groundLayers
        );

        RestorePhysicsQuery();
        return hit.collider != null;
    }

    public bool IsCeilingHit(Collider2D collider, LayerMask groundLayers, float distance)
    {
        SetupPhysicsQuery();

        var hit = Physics2D.CapsuleCast(
            collider.bounds.center,
            collider.bounds.size,
            CapsuleDirection2D.Vertical,
            0,
            Vector2.up,
            distance,
            groundLayers
        );

        RestorePhysicsQuery();
        return hit.collider != null;
    }

    public bool IsWallHit(Vector2 center, Vector2 direction, float distance, LayerMask wallLayers)
    {
        SetupPhysicsQuery();

        var hit = Physics2D.Raycast(center, direction, distance, wallLayers);

        RestorePhysicsQuery();
        return hit.collider != null;
    }

    public Collider2D GetClosestRopeSegment(Vector2 position, float radius, LayerMask ropeLayer)
    {
        return Physics2D.OverlapCircle(position, radius, ropeLayer);
    }

    public RaycastHit2D CapsuleCast(
        Vector2 center,
        Vector2 size,
        CapsuleDirection2D direction,
        float angle,
        Vector2 castDirection,
        float distance,
        LayerMask layerMask
    )
    {
        SetupPhysicsQuery();

        var hit = Physics2D.CapsuleCast(
            center,
            size,
            direction,
            angle,
            castDirection,
            distance,
            layerMask
        );

        RestorePhysicsQuery();
        return hit;
    }

    private void SetupPhysicsQuery()
    {
        Physics2D.queriesStartInColliders = false;
    }

    private void RestorePhysicsQuery()
    {
        Physics2D.queriesStartInColliders = _cachedQueryStartInColliders;
    }
}
