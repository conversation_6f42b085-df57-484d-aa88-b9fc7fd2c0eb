Unity 2D Codebase — Industry-standard Guideline for Teams

Purpose: provide an actionable, team-friendly, scalable guideline for building and maintaining a Unity 2D codebase suitable for small-to-large teams and long-lived projects.

1. Goals & Principles

Maintainability: code should be easy to read, refactor, and extend.

Modularity: features decoupled so teams can work in parallel.

Testability: logic separated from Unity engine as much as possible to allow unit testing.

Performance-aware: afford profiling-first optimizations; avoid premature micro-optimizations.

Data-driven: behavior should use data (ScriptableObjects, JSON, or Scriptable Databases) over hard-coded values.

Reproducible builds: deterministic build pipeline, version control, CI.

2. Project Setup & Tools

Use package manager / manifest to track dependencies (never check Library/). Use scoped registries if you host private packages.

Recommended packages: Addressables, Input System (new), Unity Test Framework, Cinemachine (2D) (optional), TextMeshPro, Profiler, Burst and Collections if using Jobs.

Editor tooling: install Roslyn analyzers, EditorConfig, and a shared .editorconfig.

Use Git with LFS for large assets.

Use CI (GitHub Actions / GitLab CI / Azure Pipelines) for automated builds, tests, linting.

Keep the Scripts folder small on the first level; group by feature/domain rather than by type when appropriate.

Avoid dumping everything into a single Gameplay folder — think feature verticals.

4. Feature-First Folder Organization (recommended)

Example for a Player feature:

/Scripts/Features/Player/
  PlayerController.cs
  PlayerStateMachine/
    PlayerStateBase.cs
    PlayerWalkingState.cs
  UI/
    PlayerHUDController.cs
  Tests/
    PlayerControllerTests.cs
  Editor/
    PlayerEditorUtils.cs

Benefits: feature isolation, easier ownership, simpler dependency rules.

5. Coding Conventions

Follow C# conventions (PascalCase for types and public members, camelCase for private fields with underscore _ prefix if team prefers).

Keep methods short (ideally under 50–100 lines).

Minimize use of Update() — prefer event-driven and pooled coroutines or tick managers.

Avoid Find, FindObjectOfType, or similar calls in runtime code; use DI, references, or registries.

Use readonly and const where appropriate.

Prefer explicit access modifiers (private, protected, public).

Add XML doc comments for public APIs and complex systems.

Provide a shared .editorconfig and enable style analyzers to enforce consistency.

6. Architecture Patterns

6.1. Layered / Clean-ish Architecture

Bootstrap/Composition Root — small piece of code creating and wiring systems (in Runtime/Bootstrap).

Presentation (View) — MonoBehaviours, UI adapters, visual-only logic.

Domain/Use Cases (Controller/Manager) — game rules, state machines, contains logic but no direct Unity API calls if possible.

Infrastructure — persistence, addressable loading, platform-specific code.

6.2. Systems & Services

Implement services (AudioService, SaveService, InputService) behind interfaces and register them in a lightweight service locator or DI container.

Keep service interfaces stable; allow multiple implementations (mock for tests, editor-only for debug).

6.3. ScriptableObject Data-Driven Design

Use ScriptableObjects for game tuning data, ability definitions, and shared static data.

Avoid ScriptableObjects for dynamic runtime state unless intentionally used for shared global variables.

6.4. Event/Messaging

Use a lightweight event bus for decoupling (e.g., GameEvents or IEventBus).

Prefer typed C# events or UniRx/Reactive alternative if the team is familiar.

Avoid using static mutable state unless it's immutable or truly global.

7. MonoBehaviour & Components Guidelines

Single Responsibility Principle: prefer components to do one job.

Keep MonoBehaviours as thin as possible — adaptors between Unity and domain logic.

For expensive operations, e.g., pathfinding or heavy physics checks, run them in dedicated systems rather than in many Update() calls.

Use [RequireComponent] where appropriate and validate inspector references in OnValidate().

Use Awake() for caching references, Start() for initialization dependent on other objects, and OnEnable()/OnDisable() for subscribing/unsubscribing.

8. Input System

Use the new Input System with an IInputService wrapper so gameplay code depends on your interface and not Unity API directly.

Map context-aware actions (Player, UI, Cutscene).

9. Scene & Prefab Strategy

Keep scenes small and modular — prefer additive scenes for UI, audio, environment, and gameplay.

Use a single entry scene (Bootstrap) for the game shell and then load gameplay scenes additively.

Keep prefabs atomic and reuse components.

Use naming scheme: Feature_PrefabName_v01 for clearer ownership/versioning.

10. Addressables & Asset Management

Use Addressables for large/streamed content and to enable remote content delivery.

Keep Addressables groups by usage and release channel (e.g., core, dlc, seasonal).

Automate addressable builds in CI.

11. Serialization & Save Systems

Use explicit data contracts (POCO classes) for save data; keep versioning and migration paths.

Avoid serializing UnityEngine.Object references directly in save files.

Use JSON (with strict schemas) or binary formats — ensure saves are forward/backwards compatible.

12. Testing Strategy

Unit tests for domain logic using Unity Test Framework (Edit Mode tests) — keep them fast and isolated.

Integration tests for systems interacting with Unity subsystems (Play Mode tests).

Use Mocks or Test Doubles for services.

Add tests to CI and fail builds on regressions.

13. Performance & Profiling

Profile first: use Unity Profiler and the CPU/GPU timeline.

Avoid GC allocations inside Update() — use pooling and Array/NativeArray patterns.

Use object pooling for frequently spawned objects.

Use sprite atlases & texture compression to reduce draw calls and memory.

Prefer Physics2D layers and collision matrix to reduce unnecessary checks.

14. Asynchronous & Multithreading

Use async/await carefully — prefer Unity’s coroutines for frame-synced behaviour unless using Jobs.

When using Burst/Jobs, isolate data-oriented parts and ensure thread-safety.

15. Debugging & Logging

Use Debug.Assert() liberally for runtime checks.

Avoid Console.WriteLine() in favor of logging interfaces.

18. Documentation & Onboarding

Maintain docs/ with:

Architecture overview

How to run locally

Branching strategy

Build & release steps

How to add a new feature (checklist)

Add a Getting-Started.md aimed at new devs with step-by-step setup.

Keep an up-to-date changelog for releases.

19. Asset Pipeline & Art/Audio Handoff

Provide templates and naming conventions for artists (sprite sizes, pivot rules, import settings).

Keep import presets in ProjectSettings and document required texture compression settings.

Use LFS for large raw assets (PSD, WAV) and export compressed versions for runtime.

20. Release & Versioning

Semantic versioning for builds: Major.Minor.Patch with build metadata.

Tag releases in Git and attach build artifacts.

Maintain migration notes when changing asset IDs, addressable keys, or save formats.

21. Example PR Template (short)

# Summary
What changed and why.

# Checklist
- [ ] Compiles
- [ ] Tests added/updated
- [ ] No console warnings
- [ ] Reviewed by @

# Notes
Screenshots, testing steps, known issues.

22. Example .gitignore (Unity essentials)

[Ll]ibrary/
[Tt]emp/
[Oo]bj/
[Bb]uild/
UserSettings/
/Logs/
MemoryCaptures/
*.csproj
*.unityproj
*.sln
*.user
.idea/
*.pidb
*.booproj
.vscode/
*.apk
*.aab
*.xcodeproj
.DS_Store

23. PR / Release Checklist (final)

Run smoke tests.

Verify platform-specific builds.

Run addressable/content push if required.

Update changelog and version number.

24. On-team Best Practices & Culture

Communicate ownership — each feature should have a nominated owner.

Keep the team’s coding standards documented and enforced via tools.

Hold weekly syncs and a short retro for technical debt prioritization.

Encourage small, frequent merges to reduce merge conflicts.

25. Quick-start Checklist for New Feature

Create feature branch feature/<name>.

Add folder under Scripts/Features/<name>.

Add small README explaining purpose.

Wire up services only via interfaces.

Add unit tests where possible.

Create PR when done.

26. Appendices

Naming conventions: ClassName for classes, IServiceName for interfaces, asset_name_v01 for import sources.

Time-saving tips: invest in editor tools, automated checks, and sample templates for common tasks (new feature, new prefab, new scene).

