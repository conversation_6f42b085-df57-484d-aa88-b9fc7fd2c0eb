using UnityEngine;

/// <summary>
/// Configuration data for gliding mechanics, extracted from monolithic ScriptableStats.
/// </summary>
[CreateAssetMenu(fileName = "GlidingStats", menuName = "Anchor-Light/Gliding Stats", order = 1)]
public class GlidingStats : ScriptableObject
{
    [Header("GLIDING")]
    [Toolt<PERSON>("Fall speed while gliding")]
    public float GlideFallSpeed = 2f;

    [Toolt<PERSON>("Forward speed while gliding")]
    public float GlideForwardSpeed = 8f;

    [Tooltip("Tilt angle while gliding (in degrees)")]
    public float GlideTiltAngle = 15f;

    [Toolt<PERSON>("Rotation speed while gliding")]
    public float GlideRotationSpeed = 3f;
}
