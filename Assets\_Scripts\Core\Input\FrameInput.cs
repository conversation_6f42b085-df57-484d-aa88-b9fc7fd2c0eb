using UnityEngine;

/// <summary>
/// Data structure containing all input for a single frame.
/// Moved from PlayerController to be reusable across systems.
/// </summary>
public struct FrameInput
{
    public bool JumpDown;
    public bool JumpHeld;
    public bool DashDown;
    public bool CrouchHeld;
    public bool InteractDown;
    public bool GlideHeld;
    public Vector2 Move;

    public static FrameInput Empty =>
        new FrameInput
        {
            JumpDown = false,
            JumpHeld = false,
            DashDown = false,
            CrouchHeld = false,
            InteractDown = false,
            GlideHeld = false,
            Move = Vector2.zero,
        };
}
