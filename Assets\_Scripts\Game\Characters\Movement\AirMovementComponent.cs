using UnityEngine;

/// <summary>
/// Handles air-based movement logic including gravity and air control.
/// Extracted from PlayerController to follow single responsibility principle.
/// </summary>
public class AirMovementComponent : IMovementComponent
{
    private readonly MovementStats _movementStats;
    private readonly JumpStats _jumpStats;
    private readonly GlidingStats _glidingStats;

    private bool _endedJumpEarly;

    public AirMovementComponent(
        MovementStats movementStats,
        JumpStats jumpStats,
        GlidingStats glidingStats
    )
    {
        _movementStats = movementStats;
        _jumpStats = jumpStats;
        _glidingStats = glidingStats;
    }

    public Vector2 CalculateVelocity(Vector2 currentVelocity, FrameInput input, float deltaTime)
    {
        var velocity = currentVelocity;

        // Handle gliding first (overrides normal air movement)
        if (input.GlideHeld && velocity.y < 0)
        {
            return CalculateGlidingVelocity(velocity, input, deltaTime);
        }

        // Horizontal air control
        if (input.Move.x == 0)
        {
            velocity.x = Mathf.MoveTowards(
                velocity.x,
                0,
                _movementStats.AirDeceleration * deltaTime
            );
        }
        else
        {
            velocity.x = Mathf.MoveTowards(
                velocity.x,
                input.Move.x * _movementStats.MaxSpeed,
                _movementStats.Acceleration * deltaTime
            );
        }

        // Apply gravity
        var gravityAcceleration = _jumpStats.FallAcceleration;
        if (_endedJumpEarly && velocity.y > 0)
        {
            gravityAcceleration *= _jumpStats.JumpEndEarlyGravityModifier;
        }

        velocity.y = Mathf.MoveTowards(
            velocity.y,
            -_jumpStats.MaxFallSpeed,
            gravityAcceleration * deltaTime
        );

        return velocity;
    }

    private Vector2 CalculateGlidingVelocity(
        Vector2 currentVelocity,
        FrameInput input,
        float deltaTime
    )
    {
        var velocity = currentVelocity;

        // Horizontal gliding movement
        var lastFacing = input.Move.x != 0 ? Mathf.Sign(input.Move.x) : 1f; // Default to right if no input
        velocity.x = Mathf.MoveTowards(
            velocity.x,
            lastFacing * _glidingStats.GlideForwardSpeed,
            _movementStats.Acceleration * deltaTime
        );

        // Controlled fall speed while gliding
        velocity.y = Mathf.MoveTowards(
            velocity.y,
            -_glidingStats.GlideFallSpeed,
            _jumpStats.FallAcceleration * deltaTime
        );

        return velocity;
    }

    public bool CanBeActive(IPlayerContext context)
    {
        return !context.IsGrounded
            && !context.IsSwimming
            && !context.IsWallClimbing
            && !context.IsRopeClimbing
            && !context.IsDashing;
    }

    public void OnActivated(IPlayerContext context)
    {
        // Air movement doesn't need special activation logic
    }

    public void OnDeactivated(IPlayerContext context)
    {
        _endedJumpEarly = false;
        context.SetGliding(false);
    }

    /// <summary>
    /// Called when jump is released early to modify gravity
    /// </summary>
    public void SetJumpEndedEarly(bool endedEarly)
    {
        _endedJumpEarly = endedEarly;
    }
}
