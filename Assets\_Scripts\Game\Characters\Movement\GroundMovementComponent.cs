using UnityEngine;

/// <summary>
/// Handles ground-based movement logic.
/// Extracted from PlayerController to follow single responsibility principle.
/// </summary>
public class GroundMovementComponent : IMovementComponent
{
    private readonly MovementStats _stats;

    public GroundMovementComponent(MovementStats stats)
    {
        _stats = stats;
    }

    public Vector2 CalculateVelocity(Vector2 currentVelocity, FrameInput input, float deltaTime)
    {
        var velocity = currentVelocity;

        // Horizontal movement
        if (input.Move.x == 0)
        {
            velocity.x = Mathf.MoveTowards(velocity.x, 0, _stats.GroundDeceleration * deltaTime);
        }
        else
        {
            velocity.x = Mathf.MoveTowards(
                velocity.x,
                input.Move.x * _stats.MaxSpeed,
                _stats.Acceleration * deltaTime
            );
        }

        // Apply grounding force
        if (velocity.y <= 0f)
        {
            velocity.y = _stats.GroundingForce;
        }

        return velocity;
    }

    public bool CanBeActive(IPlayerContext context)
    {
        return context.IsGrounded
            && !context.IsSwimming
            && !context.IsWallClimbing
            && !context.IsRopeClimbing
            && !context.IsDashing;
    }

    public void OnActivated(IPlayerContext context)
    {
        // Ground movement doesn't need special activation logic
    }

    public void OnDeactivated(IPlayerContext context)
    {
        // Ground movement doesn't need special deactivation logic
    }
}
