using UnityEngine;

/// <summary>
/// Service interface for physics queries and collision detection.
/// Extracted from PlayerController to separate concerns.
/// </summary>
public interface IPhysicsService
{
    /// <summary>
    /// Check if the player is grounded
    /// </summary>
    bool IsGrounded(Collider2D collider, LayerMask groundLayers, float distance);

    /// <summary>
    /// Check if the player is hitting a ceiling
    /// </summary>
    bool IsCeilingHit(Collider2D collider, LayerMask groundLayers, float distance);

    /// <summary>
    /// Check for wall collisions in a specific direction
    /// </summary>
    bool IsWallHit(Vector2 center, Vector2 direction, float distance, LayerMask wallLayers);

    /// <summary>
    /// Get the closest rope segment within range
    /// </summary>
    Collider2D GetClosestRopeSegment(Vector2 position, float radius, LayerMask ropeLayer);

    /// <summary>
    /// Perform a capsule cast for collision detection
    /// </summary>
    RaycastHit2D CapsuleCast(
        Vector2 center,
        Vector2 size,
        CapsuleDirection2D direction,
        float angle,
        Vector2 castDirection,
        float distance,
        LayerMask layerMask
    );
}
