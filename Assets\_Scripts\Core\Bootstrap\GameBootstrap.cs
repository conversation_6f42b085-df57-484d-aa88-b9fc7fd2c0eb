using UnityEngine;

/// <summary>
/// Composition root for the game. Wires up all services and systems.
/// Follows the guidelines for clean architecture with a bootstrap system.
/// </summary>
public class GameBootstrap : MonoBehaviour
{
    [Header("Service Configurations")]
    [SerializeField]
    private InputStats _inputStats;

    [Header("Debug")]
    [SerializeField]
    private bool _enableDebugLogs = true;

    private void Awake()
    {
        // Ensure this bootstrap runs before other systems
        DontDestroyOnLoad(gameObject);

        InitializeServices();
    }

    private void InitializeServices()
    {
        if (_enableDebugLogs)
            Debug.Log("GameBootstrap: Initializing services...");

        // Register core services
        RegisterInputService();
        RegisterPhysicsService();

        // Mark service locator as initialized
        ServiceLocator.MarkInitialized();

        if (_enableDebugLogs)
            Debug.Log("GameBootstrap: All services initialized successfully");
    }

    private void RegisterInputService()
    {
        // Create and configure input service
        var inputServiceGO = new GameObject("InputService");
        inputServiceGO.transform.SetParent(transform);

        var keyboardInputService = inputServiceGO.AddComponent<KeyboardInputService>();

        // Assign configuration if available
        if (_inputStats != null)
        {
            keyboardInputService.SetInputStats(_inputStats);
        }
        else
        {
            Debug.LogWarning(
                "InputStats not assigned to GameBootstrap. Input processing may not work correctly.",
                this
            );
        }

        ServiceLocator.Register<IInputService>(keyboardInputService);

        if (_enableDebugLogs)
            Debug.Log("Registered InputService");
    }

    private void RegisterPhysicsService()
    {
        var physicsService = new UnityPhysicsService();
        ServiceLocator.Register<IPhysicsService>(physicsService);

        if (_enableDebugLogs)
            Debug.Log("Registered PhysicsService");
    }

    private void OnDestroy()
    {
        // Clean up services when bootstrap is destroyed
        ServiceLocator.Clear();
    }

#if UNITY_EDITOR
    [ContextMenu("Reinitialize Services")]
    private void ReinitializeServices()
    {
        ServiceLocator.Clear();
        InitializeServices();
    }
#endif
}
