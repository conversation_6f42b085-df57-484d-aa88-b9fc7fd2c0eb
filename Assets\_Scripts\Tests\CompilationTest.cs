using UnityEngine;

/// <summary>
/// Simple test to verify that the refactored architecture compiles correctly.
/// </summary>
public class CompilationTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField]
    private MovementStats _movementStats;

    [SerializeField]
    private JumpStats _jumpStats;

    [SerializeField]
    private DashStats _dashStats;

    private void Start()
    {
        Debug.Log("CompilationTest: Testing refactored architecture...");

        // Test service locator
        TestServiceLocator();

        // Test movement components
        TestMovementComponents();

        // Test abilities
        TestAbilities();

        // Test PlayerContext compatibility
        TestPlayerContext();

        Debug.Log("CompilationTest: All tests passed!");
    }

    private void TestServiceLocator()
    {
        Debug.Log("Testing ServiceLocator...");

        // Test basic functionality
        var testService = new TestService();
        ServiceLocator.Register<ITestService>(testService);

        var retrieved = ServiceLocator.Get<ITestService>();
        if (retrieved != null)
        {
            Debug.Log("ServiceLocator: Registration and retrieval working");
        }
        else
        {
            Debug.LogError("ServiceLocator: Failed to retrieve service");
        }
    }

    private void TestMovementComponents()
    {
        Debug.Log("Testing Movement Components...");

        if (_movementStats != null)
        {
            var groundMovement = new GroundMovementComponent(_movementStats);
            Debug.Log("GroundMovementComponent: Created successfully");
        }
        else
        {
            Debug.LogWarning("MovementStats not assigned - skipping movement component test");
        }
    }

    private void TestAbilities()
    {
        Debug.Log("Testing Abilities...");

        if (_jumpStats != null && _movementStats != null)
        {
            var jumpAbility = new JumpAbility(_jumpStats, _movementStats);
            Debug.Log("JumpAbility: Created successfully");
        }
        else
        {
            Debug.LogWarning("Stats not assigned - skipping ability test");
        }

        if (_dashStats != null)
        {
            var dashAbility = new DashAbility(_dashStats);
            Debug.Log("DashAbility: Created successfully");
        }
        else
        {
            Debug.LogWarning("DashStats not assigned - skipping dash ability test");
        }
    }

    private void TestPlayerContext()
    {
        Debug.Log("Testing PlayerContext compatibility...");

        // Test with mock components
        var mockRb = gameObject.GetComponent<Rigidbody2D>();
        var mockCollider = gameObject.GetComponent<Collider2D>();

        if (mockRb == null)
        {
            mockRb = gameObject.AddComponent<Rigidbody2D>();
        }

        if (mockCollider == null)
        {
            mockCollider = gameObject.AddComponent<BoxCollider2D>();
        }

        // Test PlayerContext with generic constructor (simulating RefactoredPlayerController)
        var context = new PlayerContext(this, mockRb, mockCollider);

        // Test basic functionality
        context.SetGrounded(true);
        context.SetDashing(true);
        context.SetGliding(false);

        if (context.IsGrounded && context.IsDashing && !context.IsGliding)
        {
            Debug.Log("PlayerContext: State management working correctly");
        }
        else
        {
            Debug.LogError("PlayerContext: State management failed");
        }

        Debug.Log(
            $"PlayerContext: Rigidbody={context.Rigidbody != null}, Collider={context.Collider != null}"
        );
        Debug.Log($"PlayerContext: LastFacing={context.LastFacing}");
    }

    // Simple test service interface and implementation
    private interface ITestService
    {
        void DoSomething();
    }

    private class TestService : ITestService
    {
        public void DoSomething()
        {
            Debug.Log("TestService: DoSomething called");
        }
    }
}
