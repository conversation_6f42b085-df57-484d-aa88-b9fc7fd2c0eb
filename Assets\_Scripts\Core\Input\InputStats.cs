using UnityEngine;

/// <summary>
/// Configuration data for input handling, extracted from the monolithic ScriptableStats.
/// </summary>
[CreateAssetMenu(fileName = "InputStats", menuName = "Anchor-Light/Input Stats", order = 1)]
public class InputStats : ScriptableObject
{
    [Header("INPUT PROCESSING")]
    [Tooltip(
        "Makes all Input snap to an integer. Prevents gamepads from walking slowly. Recommended value is true to ensure gamepad/keyboard parity."
    )]
    public bool SnapInput = true;

    [Tooltip("Horizontal input threshold for snapping")]
    public float HorizontalDeadZoneThreshold = 0.1f;

    [Tooltip("Vertical input threshold for snapping")]
    public float VerticalDeadZoneThreshold = 0.1f;
}
