# Codebase Refactoring Guide

## Overview
This document outlines the refactoring of the monolithic PlayerController (758 lines) into a clean, modular architecture following the established guidelines.

## Problems Identified
1. **Monolithic PlayerController**: Single class handling input, movement, physics, abilities, and state management
2. **Monolithic ScriptableStats**: Single configuration object with mixed concerns
3. **No Service Layer**: Tight coupling between systems
4. **No Proper State Machine**: Boolean flags instead of clear state management
5. **Mixed Responsibilities**: Input, physics, and game logic all in one place

## New Architecture

### 1. Service Layer (Core/Services)
- **ServiceLocator**: Lightweight DI container for service registration
- **IInputService**: Interface for input handling with KeyboardInputService implementation
- **IPhysicsService**: Interface for physics queries with UnityPhysicsService implementation

### 2. Configuration Objects (Core/*)
Split the monolithic ScriptableStats into focused data objects:
- **InputStats**: Input processing configuration
- **MovementStats**: Basic movement parameters
- **JumpStats**: Jumping mechanics configuration
- **DashStats**: Dash ability configuration
- **SwimmingStats**: Swimming mechanics configuration
- **WallClimbingStats**: Wall climbing configuration
- **GlidingStats**: Gliding mechanics configuration

### 3. Movement Components (Game/Characters/Movement)
- **IMovementComponent**: Interface for movement behaviors
- **GroundMovementComponent**: Ground-based movement
- **AirMovementComponent**: Air movement and gliding
- **SwimmingMovementComponent**: Swimming mechanics
- **WallClimbingMovementComponent**: Wall climbing behavior

### 4. Ability System (Game/Characters/Abilities)
- **IAbility**: Interface for player abilities
- **JumpAbility**: Jump mechanics with coyote time and buffering
- **DashAbility**: Dash mechanics

### 5. State Management (Core/StateMachine)
- **IState**: Interface for state machine states
- **StateMachine**: Generic state machine implementation
- **PlayerStateBase**: Updated base class for player states

### 6. Bootstrap System (Core/Bootstrap)
- **GameBootstrap**: Composition root that wires up all services

## Migration Steps

### Phase 1: Setup New Architecture ✅
- [x] Create service interfaces and implementations
- [x] Split configuration objects
- [x] Create movement components
- [x] Create ability system
- [x] Create bootstrap system

### Phase 2: Create Transition Controller
- [ ] Create RefactoredPlayerController that uses new architecture
- [ ] Maintain backward compatibility during transition
- [ ] Test new controller alongside old one

### Phase 3: Migrate Existing Systems
- [ ] Update PlayerRopeState to use new architecture
- [ ] Update PlayerAnimator to work with new system
- [ ] Update any dependent scripts

### Phase 4: Replace Original Controller
- [ ] Replace PlayerController with RefactoredPlayerController
- [ ] Update prefabs and scene references
- [ ] Remove old monolithic code

## Benefits of New Architecture

1. **Single Responsibility**: Each class has one clear purpose
2. **Testability**: Services can be mocked for unit testing
3. **Maintainability**: Changes to one system don't affect others
4. **Extensibility**: Easy to add new movement types or abilities
5. **Configuration**: Focused configuration objects are easier to manage
6. **Performance**: Better separation allows for optimization opportunities

## Usage Example

```csharp
// In GameBootstrap (composition root)
ServiceLocator.Register<IInputService>(new KeyboardInputService());
ServiceLocator.Register<IPhysicsService>(new UnityPhysicsService());

// In RefactoredPlayerController
var inputService = ServiceLocator.Get<IInputService>();
var physicsService = ServiceLocator.Get<IPhysicsService>();

// Movement components are composed, not inherited
var groundMovement = new GroundMovementComponent(movementStats);
var jumpAbility = new JumpAbility(jumpStats, movementStats);
```

## Next Steps
1. Test the new RefactoredPlayerController
2. Create unit tests for individual components
3. Gradually migrate existing systems
4. Replace the original monolithic controller
