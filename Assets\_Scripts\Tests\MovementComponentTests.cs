using NUnit.Framework;
using UnityEngine;

/// <summary>
/// Unit tests for movement components to validate the refactored architecture.
/// </summary>
public class MovementComponentTests
{
    private MovementStats _movementStats;
    private JumpStats _jumpStats;
    private GlidingStats _glidingStats;

    [SetUp]
    public void Setup()
    {
        // Create test configuration objects
        _movementStats = ScriptableObject.CreateInstance<MovementStats>();
        _movementStats.MaxSpeed = 10f;
        _movementStats.Acceleration = 100f;
        _movementStats.GroundDeceleration = 50f;
        _movementStats.AirDeceleration = 25f;

        _jumpStats = ScriptableObject.CreateInstance<JumpStats>();
        _jumpStats.FallAcceleration = 100f;
        _jumpStats.MaxFallSpeed = 20f;

        _glidingStats = ScriptableObject.CreateInstance<GlidingStats>();
        _glidingStats.GlideFallSpeed = 2f;
        _glidingStats.GlideForwardSpeed = 8f;
    }

    [Test]
    public void GroundMovement_WithHorizontalInput_AcceleratesCorrectly()
    {
        // Arrange
        var groundMovement = new GroundMovementComponent(_movementStats);
        var input = new FrameInput { Move = new Vector2(1, 0) };
        var currentVelocity = Vector2.zero;

        // Act
        var newVelocity = groundMovement.CalculateVelocity(currentVelocity, input, 0.1f);

        // Assert
        Assert.Greater(newVelocity.x, 0, "Should accelerate in positive direction");
        Assert.LessOrEqual(newVelocity.x, _movementStats.MaxSpeed, "Should not exceed max speed");
    }

    [Test]
    public void GroundMovement_WithNoInput_DeceleratesCorrectly()
    {
        // Arrange
        var groundMovement = new GroundMovementComponent(_movementStats);
        var input = new FrameInput { Move = Vector2.zero };
        var currentVelocity = new Vector2(5f, 0);

        // Act
        var newVelocity = groundMovement.CalculateVelocity(currentVelocity, input, 0.1f);

        // Assert
        Assert.Less(newVelocity.x, currentVelocity.x, "Should decelerate when no input");
    }

    [Test]
    public void AirMovement_WithGlideInput_LimitsGlideFallSpeed()
    {
        // Arrange
        var airMovement = new AirMovementComponent(_movementStats, _jumpStats, _glidingStats);
        var input = new FrameInput { GlideHeld = true, Move = new Vector2(1, 0) };
        var currentVelocity = new Vector2(0, -10f); // Falling fast

        // Act
        var newVelocity = airMovement.CalculateVelocity(currentVelocity, input, 0.1f);

        // Assert
        Assert.GreaterOrEqual(
            newVelocity.y,
            -_glidingStats.GlideFallSpeed - 0.1f,
            "Should limit fall speed while gliding"
        );
    }

    [TearDown]
    public void TearDown()
    {
        // Clean up ScriptableObjects
        if (_movementStats != null)
            Object.DestroyImmediate(_movementStats);
        if (_jumpStats != null)
            Object.DestroyImmediate(_jumpStats);
        if (_glidingStats != null)
            Object.DestroyImmediate(_glidingStats);
    }
}
