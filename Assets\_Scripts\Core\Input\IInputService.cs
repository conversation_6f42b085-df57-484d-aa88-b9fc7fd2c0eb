using UnityEngine;

/// <summary>
/// Service interface for handling player input.
/// Allows for different implementations (keyboard, gamepad, AI, etc.) and easy testing.
/// </summary>
public interface IInputService
{
    /// <summary>
    /// Current frame's input data
    /// </summary>
    FrameInput CurrentInput { get; }

    /// <summary>
    /// Last direction the player was facing (-1 or 1)
    /// </summary>
    float LastFacing { get; }

    /// <summary>
    /// Updates input for the current frame. Call this once per frame.
    /// </summary>
    void UpdateInput();

    /// <summary>
    /// Consumes the jump input (sets JumpDown to false)
    /// </summary>
    void ConsumeJump();

    /// <summary>
    /// Consumes the dash input (sets DashDown to false)
    /// </summary>
    void ConsumeDash();
}
