using UnityEngine;
using AnchorLight.Game.Characters;

/// <summary>
/// Test script to verify that the circular reference issue between 
/// RefactoredPlayerController.IsRopeClimbing and PlayerContext.IsRopeClimbing is fixed.
/// </summary>
public class RopeClimbingCircularReferenceTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool runTestOnStart = true;
    [SerializeField] private bool logResults = true;

    private void Start()
    {
        if (runTestOnStart)
        {
            TestRopeClimbingCircularReference();
        }
    }

    [ContextMenu("Test Rope Climbing Circular Reference")]
    public void TestRopeClimbingCircularReference()
    {
        if (logResults)
            Debug.Log("Starting Rope Climbing Circular Reference Test...");

        try
        {
            // Create a test GameObject with required components
            GameObject testPlayer = new GameObject("TestPlayer");
            testPlayer.AddComponent<Rigidbody2D>();
            testPlayer.AddComponent<CapsuleCollider2D>();
            
            // Add the RefactoredPlayerController
            RefactoredPlayerController controller = testPlayer.AddComponent<RefactoredPlayerController>();
            
            // Wait one frame for initialization
            StartCoroutine(TestAfterInitialization(controller));
        }
        catch (System.Exception e)
        {
            if (logResults)
                Debug.LogError($"Test failed with exception: {e.Message}\n{e.StackTrace}");
        }
    }

    private System.Collections.IEnumerator TestAfterInitialization(RefactoredPlayerController controller)
    {
        yield return null; // Wait one frame for Awake to complete

        try
        {
            // Test 1: Check that IsRopeClimbing can be accessed without stack overflow
            bool initialState = controller.IsRopeClimbing;
            if (logResults)
                Debug.Log($"✓ Test 1 Passed: IsRopeClimbing accessed successfully. Initial state: {initialState}");

            // Test 2: Test setting rope climbing state
            controller.SetRopeClimbing(true);
            bool afterSet = controller.IsRopeClimbing;
            if (logResults)
                Debug.Log($"✓ Test 2 Passed: SetRopeClimbing(true) worked. State: {afterSet}");

            // Test 3: Test unsetting rope climbing state
            controller.SetRopeClimbing(false);
            bool afterUnset = controller.IsRopeClimbing;
            if (logResults)
                Debug.Log($"✓ Test 3 Passed: SetRopeClimbing(false) worked. State: {afterUnset}");

            // Test 4: Multiple rapid accesses (would cause stack overflow if circular reference exists)
            for (int i = 0; i < 100; i++)
            {
                bool state = controller.IsRopeClimbing;
                controller.SetRopeClimbing(i % 2 == 0);
            }
            if (logResults)
                Debug.Log("✓ Test 4 Passed: Multiple rapid accesses completed without stack overflow");

            if (logResults)
                Debug.Log("🎉 All tests passed! Circular reference issue is fixed.");
        }
        catch (System.Exception e)
        {
            if (logResults)
                Debug.LogError($"❌ Test failed: {e.Message}\n{e.StackTrace}");
        }
        finally
        {
            // Clean up test object
            if (controller != null && controller.gameObject != null)
            {
                DestroyImmediate(controller.gameObject);
            }
        }
    }
}
