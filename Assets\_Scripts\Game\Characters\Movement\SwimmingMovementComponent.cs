using UnityEngine;

/// <summary>
/// <PERSON>les swimming movement logic.
/// Extracted from PlayerController to follow single responsibility principle.
/// </summary>
public class SwimmingMovementComponent : IMovementComponent
{
    private readonly SwimmingStats _stats;

    public SwimmingMovementComponent(SwimmingStats stats)
    {
        _stats = stats;
    }

    public Vector2 CalculateVelocity(Vector2 currentVelocity, FrameInput input, float deltaTime)
    {
        var velocity = currentVelocity;
        var move = input.Move;

        // Get water surface information from context
        // Note: This will need to be passed in or accessed through context
        // For now, we'll handle basic swimming logic

        // Calculate movement based on input
        if (move.magnitude > 0.1f)
        {
            velocity = move.normalized * _stats.SwimSpeed;
        }
        else
        {
            // Apply drag when there's no input
            velocity = Vector2.MoveTowards(velocity, Vector2.zero, _stats.SwimDrag * deltaTime);
        }

        return velocity;
    }

    /// <summary>
    /// Calculate swimming velocity with water surface constraints
    /// </summary>
    public Vector2 CalculateVelocityWithWaterSurface(
        Vector2 currentVelocity,
        FrameInput input,
        float deltaTime,
        Collider2D playerCollider,
        Collider2D waterCollider
    )
    {
        if (waterCollider == null || playerCollider == null)
            return CalculateVelocity(currentVelocity, input, deltaTime);

        var move = input.Move;
        var waterSurfaceY = waterCollider.bounds.max.y;
        var playerTopY = playerCollider.bounds.max.y;

        // Prevent upward movement if at or above the surface
        if (playerTopY >= waterSurfaceY && move.y > 0)
        {
            move.y = 0;
        }

        var velocity = currentVelocity;

        // Calculate horizontal and vertical velocity based on input
        if (move.magnitude > 0.1f)
        {
            velocity = move.normalized * _stats.SwimSpeed;
        }
        else
        {
            // Apply drag when there's no input
            velocity = Vector2.MoveTowards(velocity, Vector2.zero, _stats.SwimDrag * deltaTime);
        }

        // Apply buoyancy only when submerged and not actively moving vertically
        if (playerTopY < waterSurfaceY && Mathf.Abs(move.y) < 0.1f)
        {
            velocity.y = Mathf.MoveTowards(
                velocity.y,
                _stats.Buoyancy,
                _stats.SwimDrag * deltaTime // Using swim drag for buoyancy rate
            );
        }

        return velocity;
    }

    public bool CanBeActive(IPlayerContext context)
    {
        return context.IsSwimming;
    }

    public void OnActivated(IPlayerContext context)
    {
        // Swimming doesn't need special activation logic
    }

    public void OnDeactivated(IPlayerContext context)
    {
        // Swimming doesn't need special deactivation logic
    }
}
