using UnityEngine;

/// <summary>
/// Interface for player controllers that can provide context to PlayerContext.
/// Allows PlayerContext to work with both original and refactored controllers.
/// </summary>
public interface IPlayerContextProvider
{
    Rigidbody2D RB { get; }
    Collider2D Col { get; }
    bool IsRopeClimbing { get; }
    float LastFacing { get; }
    bool IsDashing { get; }
}

/// <summary>
/// Implementation of IPlayerContext that provides access to player state.
/// Acts as a bridge between controllers and the new component system.
/// Now supports both PlayerController and RefactoredPlayerController.
/// </summary>
public class PlayerContext : IPlayerContext
{
    private readonly IPlayerContextProvider _provider;
    private readonly MonoBehaviour _controller;
    private bool _isGrounded;
    private bool _isSwimming;
    private bool _isWallClimbing;
    private bool _isGliding;
    private bool _isDashing;
    private bool _isRopeClimbing;
    private Collider2D _waterCollider;

    // Constructor for original PlayerController
    public PlayerContext(PlayerController playerController)
    {
        _provider = new PlayerControllerAdapter(playerController);
        _controller = playerController;
    }

    // Constructor for RefactoredPlayerController
    public PlayerContext(MonoBehaviour controller, Rigidbody2D rb, Collider2D collider)
    {
        // Check if the controller implements IPlayerContextProvider directly
        if (controller is IPlayerContextProvider contextProvider)
        {
            _provider = contextProvider;
        }
        else
        {
            _provider = new GenericControllerAdapter(rb, collider, controller);
        }
        _controller = controller;
    }

    // Physics components
    public Rigidbody2D Rigidbody => _provider.RB;
    public Collider2D Collider => _provider.Col;

    // State queries
    public bool IsGrounded => _isGrounded;
    public bool IsSwimming => _isSwimming;
    public bool IsWallClimbing => _isWallClimbing;
    public bool IsRopeClimbing =>
        _controller is AnchorLight.Game.Characters.RefactoredPlayerController
            ? _isRopeClimbing
            : _provider.IsRopeClimbing;
    public bool IsDashing => _isDashing || _provider.IsDashing;
    public bool IsGliding => _isGliding;

    // Environmental context
    public Collider2D WaterCollider => _waterCollider;
    public float LastFacing => _provider.LastFacing;

    // State setters
    public void SetGrounded(bool grounded)
    {
        _isGrounded = grounded;
    }

    public void SetSwimming(bool swimming, Collider2D waterCollider = null)
    {
        _isSwimming = swimming;
        _waterCollider = waterCollider;
    }

    public void SetWallClimbing(bool wallClimbing)
    {
        _isWallClimbing = wallClimbing;
    }

    public void SetGliding(bool gliding)
    {
        _isGliding = gliding;
    }

    public void SetDashing(bool dashing)
    {
        _isDashing = dashing;
    }

    public void SetRopeClimbing(bool ropeClimbing)
    {
        _isRopeClimbing = ropeClimbing;
    }
}

/// <summary>
/// Adapter for the original PlayerController to work with IPlayerContextProvider.
/// </summary>
internal class PlayerControllerAdapter : IPlayerContextProvider
{
    private readonly PlayerController _playerController;

    public PlayerControllerAdapter(PlayerController playerController)
    {
        _playerController = playerController;
    }

    public Rigidbody2D RB => _playerController.RB;
    public Collider2D Col => _playerController.Col;
    public bool IsRopeClimbing => _playerController.IsRopeClimbing;
    public float LastFacing => _playerController._lastFacing;
    public bool IsDashing => false; // Original controller doesn't expose this properly
}

/// <summary>
/// Generic adapter for RefactoredPlayerController and other controllers.
/// </summary>
internal class GenericControllerAdapter : IPlayerContextProvider
{
    private readonly Rigidbody2D _rb;
    private readonly Collider2D _collider;
    private readonly MonoBehaviour _controller;

    public GenericControllerAdapter(
        Rigidbody2D rb,
        Collider2D collider,
        MonoBehaviour controller = null
    )
    {
        _rb = rb;
        _collider = collider;
        _controller = controller;
    }

    public Rigidbody2D RB => _rb;
    public Collider2D Col => _collider;
    public bool IsRopeClimbing => false; // RefactoredPlayerController manages this separately

    public float LastFacing
    {
        get
        {
            // Try to get LastFacing from RefactoredPlayerController if available
            if (_controller != null && _controller.GetType().Name == "RefactoredPlayerController")
            {
                var lastFacingProperty = _controller.GetType().GetProperty("_lastFacing");
                if (lastFacingProperty != null)
                {
                    return (float)lastFacingProperty.GetValue(_controller);
                }
            }
            return 1f; // Default facing right
        }
    }

    public bool IsDashing => false; // RefactoredPlayerController manages this through abilities
}
