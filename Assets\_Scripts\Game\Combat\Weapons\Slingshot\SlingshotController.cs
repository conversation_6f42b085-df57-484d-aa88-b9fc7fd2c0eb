using UnityEngine;
using AnchorLight.Core.ScriptableObjects;

namespace AnchorLight.Game.Combat.Weapons.Slingshot
{
    [RequireComponent(typeof(ProjectilePool))]
    public class SlingshotController : WeaponBase
    {
        [SerializeField] private SlingshotStats _stats;
        [SerializeField] private ProjectilePool _pool;

        private float _chargeTimer;
        private bool _isPulling;
        private SlingshotProjectile _previewProjectile;

        private void Awake()
        {
            if (_pool == null) _pool = GetComponent<ProjectilePool>();
        }

        private void Update()
        {
            // Begin pull (e.g. left mouse button)
            if (Input.GetButtonDown("Fire1")) PullStart();
            if (Input.GetButton("Fire1")) PullUpdate();
            if (Input.GetButtonUp("Fire1")) ReleaseFire();
        }

        public override void PullStart()
        {
            _chargeTimer = 0f;
            _isPulling = true;
            // Spawn preview projectile
            _previewProjectile = _pool.Get();
            _previewProjectile.transform.position = transform.position;
            // Disable physics and collision for preview
            var previewRb = _previewProjectile.GetComponent<Rigidbody2D>();
            previewRb.linearVelocity = Vector2.zero;
            previewRb.angularVelocity = 0f;
            previewRb.isKinematic = true;
            var previewCol = _previewProjectile.GetComponent<Collider2D>();
            previewCol.enabled = false;
            // TODO: trigger pull visuals (e.g. line renderer)
        }

        public override void PullUpdate()
        {
            if (!_isPulling) return;
            _chargeTimer = Mathf.Min(_stats.MaxChargeTime, _chargeTimer + Time.deltaTime);
            // Update preview position to slingshot origin
            if (_previewProjectile != null)
                _previewProjectile.transform.position = transform.position;
            // TODO: update pull visuals based on _chargeTimer / _stats.MaxChargeTime
        }

        public override void ReleaseFire()
        {
            if (!_isPulling) return;
            _isPulling = false;
            // Calculate strength
            float normalized = _stats.MaxChargeTime > 0 ? (_chargeTimer / _stats.MaxChargeTime) : 1f;
            float strength = Mathf.Clamp01(normalized) * _stats.MaxPullStrength;
            // Compute direction from slingshot to cursor
            Vector3 mouseWorld = Camera.main.ScreenToWorldPoint(Input.mousePosition);
            Vector2 dir = (mouseWorld - transform.position).normalized;
            if (_previewProjectile != null)
            {
                // Re-enable physics and collision for actual fire
                var fireRb = _previewProjectile.GetComponent<Rigidbody2D>();
                fireRb.isKinematic = false;
                var fireCol = _previewProjectile.GetComponent<Collider2D>();
                fireCol.enabled = true;
                _previewProjectile.Fire(dir, strength * _stats.ShotSpeed);
                _previewProjectile = null;
            }
            // TODO: trigger release effects
        }
    }
}