using UnityEditor; // We need this namespace for editor scripting
using UnityEngine;

// This attribute tells Unity that this script is a custom editor
// for the WaterMeshGenerator class.
[CustomEditor(typeof(WaterMeshGenerator))]
public class WaterMeshGeneratorEditor : Editor
{
    // This method is called to draw the custom GUI in the Inspector.
    public override void OnInspectorGUI()
    {
        // Draw the default fields (width, height, subdivisions).
        base.OnInspectorGUI();

        // Add some space for visual separation.
        EditorGUILayout.Space();

        // Get a reference to the script we are editing.
        WaterMeshGenerator generator = (WaterMeshGenerator)target;

        // Create a big, noticeable button. If it's clicked...
        if (GUILayout.Button("Generate Water Mesh", GUILayout.Height(30)))
        {
            // ...call the public GenerateMesh method on our target script.
            generator.GenerateMesh();
        }
    }
}
