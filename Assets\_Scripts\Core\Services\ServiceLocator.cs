using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Lightweight service locator for dependency injection.
/// Follows the guidelines for clean architecture with stable service interfaces.
/// </summary>
public static class ServiceLocator
{
    private static readonly Dictionary<Type, object> _services = new Dictionary<Type, object>();
    private static bool _isInitialized = false;

    /// <summary>
    /// Register a service implementation
    /// </summary>
    public static void Register<T>(T implementation)
        where T : class
    {
        var type = typeof(T);
        if (_services.ContainsKey(type))
        {
            Debug.LogWarning($"Service {type.Name} is already registered. Overwriting.");
        }

        _services[type] = implementation;
        Debug.Log($"Registered service: {type.Name}");
    }

    /// <summary>
    /// Get a service implementation
    /// </summary>
    public static T Get<T>()
        where T : class
    {
        var type = typeof(T);
        if (_services.TryGetValue(type, out var service))
        {
            return service as T;
        }

        Debug.LogError(
            $"Service {type.Name} not found. Make sure it's registered in the bootstrap."
        );
        return null;
    }

    /// <summary>
    /// Check if a service is registered
    /// </summary>
    public static bool IsRegistered<T>()
        where T : class
    {
        return _services.ContainsKey(typeof(T));
    }

    /// <summary>
    /// Unregister a service (useful for testing)
    /// </summary>
    public static void Unregister<T>()
        where T : class
    {
        var type = typeof(T);
        if (_services.Remove(type))
        {
            Debug.Log($"Unregistered service: {type.Name}");
        }
    }

    /// <summary>
    /// Clear all services (useful for testing and cleanup)
    /// </summary>
    public static void Clear()
    {
        _services.Clear();
        _isInitialized = false;
        Debug.Log("ServiceLocator cleared");
    }

    /// <summary>
    /// Mark the service locator as initialized
    /// </summary>
    public static void MarkInitialized()
    {
        _isInitialized = true;
    }

    /// <summary>
    /// Check if the service locator has been initialized
    /// </summary>
    public static bool IsInitialized => _isInitialized;
}
