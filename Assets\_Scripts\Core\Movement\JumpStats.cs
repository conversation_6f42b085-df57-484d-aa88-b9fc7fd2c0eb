using UnityEngine;

/// <summary>
/// Configuration data for jumping mechanics, extracted from monolithic ScriptableStats.
/// </summary>
[CreateAssetMenu(fileName = "JumpStats", menuName = "Anchor-Light/Jump Stats", order = 1)]
public class JumpStats : ScriptableObject
{
    [Header("JUMP")]
    [Tooltip("The immediate velocity applied when jumping")]
    public float JumpPower = 36;

    [Tooltip("The maximum vertical movement speed")]
    public float MaxFallSpeed = 40;

    [Tooltip("The player's capacity to gain fall speed. a.k.a. In Air Gravity")]
    public float FallAcceleration = 110;

    [Toolt<PERSON>("The gravity multiplier added when jump is released early")]
    public float JumpEndEarlyGravityModifier = 3;

    [<PERSON>lt<PERSON>(
        "The time before coyote jump becomes unusable. Coyote jump allows jump to execute even after leaving a ledge"
    )]
    public float CoyoteTime = .15f;

    [Tooltip(
        "The amount of time we buffer a jump. This allows jump input before actually hitting the ground"
    )]
    public float JumpBuffer = .2f;
}
