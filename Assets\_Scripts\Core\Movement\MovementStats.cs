using UnityEngine;

/// <summary>
/// Configuration data for basic movement, extracted from monolithic ScriptableStats.
/// </summary>
[CreateAssetMenu(fileName = "MovementStats", menuName = "Anchor-Light/Movement Stats", order = 1)]
public class MovementStats : ScriptableObject
{
    [Header("BASIC MOVEMENT")]
    [Tooltip("The top horizontal movement speed")]
    public float MaxSpeed = 14;

    [Tooltip("The player's capacity to gain horizontal speed")]
    public float Acceleration = 120;

    [Tooltip("The pace at which the player comes to a stop")]
    public float GroundDeceleration = 60;

    [Tooltip("Deceleration in air only after stopping input mid-air")]
    public float AirDeceleration = 30;

    [Tooltip("A constant downward force applied while grounded. Helps on slopes"), Range(0f, -10f)]
    public float GroundingForce = -1.5f;

    [Tooltip("The detection distance for grounding and roof detection"), Range(0f, 0.5f)]
    public float GrounderDistance = 0.05f;
}
