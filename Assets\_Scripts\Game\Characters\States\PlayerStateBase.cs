using AnchorLight.Game.Characters;
using UnityEngine;

public abstract class PlayerStateBase
{
    protected readonly PlayerController _player;
    protected readonly PlayerAnimator _animator;

    protected PlayerStateBase(PlayerController player, PlayerAnimator animator)
    {
        _player = player;
        _animator = animator;
    }

    public virtual void Enter() { }

    public virtual void Update() { }

    public virtual void FixedUpdate() { }

    public virtual void Exit() { }
}
