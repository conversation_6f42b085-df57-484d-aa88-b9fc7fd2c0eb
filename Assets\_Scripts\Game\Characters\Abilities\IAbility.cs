using UnityEngine;

/// <summary>
/// Interface for player abilities (jump, dash, glide, etc.).
/// Allows for composable ability system.
/// </summary>
public interface IAbility
{
    /// <summary>
    /// Check if this ability can be activated given current conditions
    /// </summary>
    bool CanActivate(IPlayerContext context, FrameInput input);

    /// <summary>
    /// Attempt to activate the ability
    /// </summary>
    bool TryActivate(IPlayerContext context, FrameInput input);

    /// <summary>
    /// Update the ability (called every frame while active)
    /// </summary>
    void Update(float deltaTime);

    /// <summary>
    /// Fixed update the ability (called every fixed frame while active)
    /// </summary>
    void FixedUpdate(float fixedDeltaTime);

    /// <summary>
    /// Check if the ability is currently active
    /// </summary>
    bool IsActive { get; }

    /// <summary>
    /// Force deactivate the ability
    /// </summary>
    void Deactivate();
}
