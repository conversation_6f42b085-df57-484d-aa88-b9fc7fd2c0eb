using System.Collections;
using UnityEngine;

public class BouncePad : MonoBeh<PERSON>our
{
    [field: SerializeField]
    public float BounceForce { get; private set; } = 20f;

    [SerializeField]
    private float bounceCooldown = 0.5f;

    [SerializeField]
    private Animator animator;

    [SerializeField]
    private string bounceAnimationTrigger = "Bounce";

    [SerializeField]
    private AudioSource audioSource;

    [SerializeField]
    private AudioClip bounceSFX;

    private bool _canBounce = true;

    public bool CanBounce => _canBounce;

    public void TriggerEffects()
    {
        if (animator != null)
            animator.SetTrigger(bounceAnimationTrigger);

        if (audioSource != null && bounceSFX != null)
            audioSource.PlayOneShot(bounceSFX);

        StartCoroutine(BounceCooldown());
    }

    private IEnumerator BounceCooldown()
    {
        _canBounce = false;
        yield return new WaitForSeconds(bounceCooldown);
        _canBounce = true;
    }
}
