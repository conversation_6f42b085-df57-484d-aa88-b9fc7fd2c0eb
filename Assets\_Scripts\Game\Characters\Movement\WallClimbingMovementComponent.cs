using UnityEngine;

/// <summary>
/// Handles wall climbing movement logic.
/// Extracted from PlayerController to follow single responsibility principle.
/// </summary>
public class WallClimbingMovementComponent : IMovementComponent
{
    private readonly WallClimbingStats _stats;
    private float _wallGripTimeLeft;
    private int _wallDirection; // -1 for left wall, 1 for right wall

    public int WallDirection => _wallDirection;

    public WallClimbingMovementComponent(WallClimbingStats stats)
    {
        _stats = stats;
    }

    public Vector2 CalculateVelocity(Vector2 currentVelocity, FrameInput input, float deltaTime)
    {
        var velocity = currentVelocity;

        // Update grip timer
        _wallGripTimeLeft -= deltaTime;

        // If grip time expired, start sliding down
        if (_wallGripTimeLeft <= 0)
        {
            velocity.x = 0;
            velocity.y = Mathf.MoveTowards(
                velocity.y,
                -_stats.WallSlideSpeed,
                _stats.WallSlideSpeed * deltaTime
            );
        }
        else
        {
            // Still gripping - no movement
            velocity.x = 0;
            velocity.y = 0;
        }

        return velocity;
    }

    public bool CanBeActive(IPlayerContext context)
    {
        return context.IsWallClimbing
            && !context.IsGrounded
            && !context.IsSwimming
            && !context.IsRopeClimbing
            && !context.IsDashing;
    }

    public void OnActivated(IPlayerContext context)
    {
        _wallGripTimeLeft = _stats.WallGripDuration;
        // Wall direction should be set by the collision detection system
    }

    public void OnDeactivated(IPlayerContext context)
    {
        _wallGripTimeLeft = 0;
        _wallDirection = 0;
    }

    /// <summary>
    /// Set the wall direction when attaching to a wall
    /// </summary>
    public void SetWallDirection(int direction)
    {
        _wallDirection = direction;
    }

    /// <summary>
    /// Calculate wall jump velocity
    /// </summary>
    public Vector2 CalculateWallJumpVelocity(FrameInput input)
    {
        Vector2 jumpDirection;

        if (input.Move.x != 0)
        {
            // Jump in the direction the player is pointing
            jumpDirection = new Vector2(
                input.Move.x * _stats.WallJumpForce.x,
                _stats.WallJumpForce.y
            );
        }
        else
        {
            // Default jump away from wall
            jumpDirection = new Vector2(
                -_wallDirection * _stats.WallJumpForce.x,
                _stats.WallJumpForce.y
            );
        }

        return jumpDirection;
    }
}
