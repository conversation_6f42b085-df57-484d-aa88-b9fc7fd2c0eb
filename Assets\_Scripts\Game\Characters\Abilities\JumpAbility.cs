using System;
using UnityEngine;

/// <summary>
/// <PERSON>les jumping logic including coyote time and jump buffering.
/// Extracted from PlayerController to follow single responsibility principle.
/// </summary>
public class JumpAbility : IAbility
{
    private readonly JumpStats _stats;
    private readonly MovementStats _movementStats;

    private bool _jumpToConsume;
    private bool _bufferedJumpUsable;
    private bool _coyoteUsable;
    private float _timeJumpWasPressed;
    private float _frameLeftGrounded = float.MinValue;
    private float _currentTime;

    public event Action Jumped;

    public bool IsActive => false; // Jump is instantaneous, not a persistent state

    public JumpAbility(JumpStats stats, MovementStats movementStats)
    {
        _stats = stats;
        _movementStats = movementStats;
    }

    public bool CanActivate(IPlayerContext context, FrameInput input)
    {
        return (context.IsGrounded || CanUseCoyote()) && !context.IsSwimming && !context.IsDashing;
    }

    public bool TryActivate(IPlayerContext context, FrameInput input)
    {
        if (!input.JumpDown && !HasBufferedJump())
            return false;

        if (!CanActivate(context, input))
            return false;

        ExecuteJump(context);
        return true;
    }

    public void Update(float deltaTime)
    {
        _currentTime += deltaTime;

        // Track jump input for buffering
        // Note: This should be called by the input service, but we handle it here for now
    }

    public void FixedUpdate(float fixedDeltaTime)
    {
        // Jump is instantaneous, no fixed update needed
    }

    public void Deactivate()
    {
        // Jump doesn't have a persistent active state
    }

    /// <summary>
    /// Set jump input for buffering
    /// </summary>
    public void SetJumpInput(bool jumpPressed, float currentTime)
    {
        if (jumpPressed)
        {
            _jumpToConsume = true;
            _timeJumpWasPressed = currentTime;
        }
    }

    /// <summary>
    /// Called when player leaves ground for coyote time
    /// </summary>
    public void OnLeftGround(float currentTime)
    {
        _frameLeftGrounded = currentTime;
    }

    /// <summary>
    /// Called when player touches ground
    /// </summary>
    public void OnGrounded()
    {
        _coyoteUsable = true;
        _bufferedJumpUsable = true;
    }

    private bool HasBufferedJump()
    {
        return _bufferedJumpUsable && _currentTime < _timeJumpWasPressed + _stats.JumpBuffer;
    }

    private bool CanUseCoyote()
    {
        return _coyoteUsable && _currentTime < _frameLeftGrounded + _stats.CoyoteTime;
    }

    private void ExecuteJump(IPlayerContext context)
    {
        // Preserve horizontal momentum while applying vertical jump power
        var currentVelocity = context.Rigidbody.linearVelocity;
        var jumpVelocity = new Vector2(currentVelocity.x, _stats.JumpPower);

        context.Rigidbody.linearVelocity = jumpVelocity;

        // Reset jump state
        _jumpToConsume = false;
        _timeJumpWasPressed = 0;
        _bufferedJumpUsable = false;
        _coyoteUsable = false;

        Jumped?.Invoke();
    }

    /// <summary>
    /// Calculate wall jump velocity (used by wall climbing component)
    /// </summary>
    public Vector2 CalculateWallJumpVelocity(Vector2 wallJumpForce)
    {
        return wallJumpForce;
    }
}
