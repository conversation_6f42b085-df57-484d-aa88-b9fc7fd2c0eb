using UnityEngine;

/// <summary>
/// Configuration data for swimming mechanics, extracted from monolithic ScriptableStats.
/// </summary>
[CreateAssetMenu(fileName = "SwimmingStats", menuName = "Anchor-Light/Swimming Stats", order = 1)]
public class SwimmingStats : ScriptableObject
{
    [Header("SWIMMING")]
    [Toolt<PERSON>("Movement speed while swimming")]
    public float SwimSpeed = 5f;

    [Tooltip("The upward force applied when submerged")]
    public float Buoyancy = 5f;

    [Tooltip("The drag applied when not moving in water")]
    public float SwimDrag = 2f;

    [Tooltip("Rotation speed while swimming")]
    public float SwimRotationSpeed = 5f;

    [Header("DIVING")]
    [Tooltip("Vertical height reduction when diving (in world units)")]
    [Range(0f, 2f)]
    public float DiveHeightReduction = 0.5f;
}
