using AnchorLight.Core;
using AnchorLight.Game.Characters;
using Mono.Cecil.Cil;
using UnityEngine;

public class PlayerRopeState : PlayerStateBase
{
    private readonly ScriptableStats _stats;
    private Rope _currentRope;
    private float _timeSinceLastClimb;
    private Rigidbody2D _playerRb;
    private float _originalGravityScale;
    private RigidbodyType2D _originalBodyType;
    private float _detachCooldownTime; // NEW: Track cooldown after detaching
    private Rope _lastDetachedRope; // NEW: Track which rope we just detached from

    public PlayerRopeState(PlayerController player, PlayerAnimator animator, ScriptableStats stats)
        : base(player, animator)
    {
        _stats = stats;
    }

    public override void Enter()
    {
        base.Enter();
        // This state requires a rope to be passed in, so we'll use the other Enter method
    }

    public void Enter(Rope rope)
    {
        _playerRb = _player.GetComponent<Rigidbody2D>();
        _originalGravityScale = _playerRb.gravityScale;
        _originalBodyType = _playerRb.bodyType;

        // Store current velocity before changing physics
        Vector2 currentVelocity = _playerRb.linearVelocity;

        // Reduce gravity when on rope - let rope physics handle most of the motion
        _playerRb.gravityScale = 0.3f; // Reduced gravity for better rope physics
        _playerRb.bodyType = RigidbodyType2D.Dynamic;

        AttachToRope(rope, _player.transform.position);

        // Preserve horizontal momentum when attaching to rope for smooth transition
        Vector2 preservedVelocity = new Vector2(currentVelocity.x * 0.8f, currentVelocity.y * 0.5f);
        _playerRb.linearVelocity = preservedVelocity;

        _timeSinceLastClimb = 0;

        // NEW: Clear cooldown when successfully attaching to a rope
        _detachCooldownTime = 0;
        _lastDetachedRope = null;
    }

    public override void Exit()
    {
        if (_currentRope != null)
        {
            _currentRope.Detach();

            // NEW: Remember which rope we detached from
            _lastDetachedRope = _currentRope;
        }

        if (_playerRb != null)
        {
            _playerRb.gravityScale = _originalGravityScale;
            _playerRb.bodyType = _originalBodyType;
        }

        _currentRope = null;
        _player.IsRopeClimbing = false;

        // NEW: Set local cooldown to prevent re-detection
        _detachCooldownTime = _stats.RopeCooldown;
    }

    public override void FixedUpdate()
    {
        // NEW: Update detach cooldown
        if (_detachCooldownTime > 0)
        {
            _detachCooldownTime -= Time.fixedDeltaTime;
        }

        // Only check for rope if not on cooldown
        if (_detachCooldownTime <= 0)
        {
            CheckForRope();
        }

        if (_currentRope == null)
            return;

        // Update smooth climbing interpolation
        _currentRope.UpdateClimbing();

        _timeSinceLastClimb += Time.fixedDeltaTime;

        HandleRopeInput();
    }

    private void HandleRopeInput()
    {
        // Priority 1: Check for manual detachment (E key)
        if (_player.FrameInputStruct.InteractDown)
        {
            Debug.Log("E key pressed - detaching from rope");
            _player.ToggleRope(false);
            return;
        }

        // Priority 2: Check if player has climbed below the rope (auto-detach)
        if (CheckIfBelowRope())
        {
            Debug.Log("Player below rope - auto-detaching");
            _player.ToggleRope(false);
            return;
        }

        // Priority 3: Handle jump off rope
        if (_player.JumpToConsume)
        {
            ExecuteRopeJump();
            return;
        }

        // Priority 4: Handle climbing
        if (
            _timeSinceLastClimb >= _stats.RopeClimbInputCooldown
            && Mathf.Abs(_player.FrameInputStruct.Move.y) > _stats.VerticalDeadZoneThreshold
        )
        {
            float climbDirection = Mathf.Sign(_player.FrameInputStruct.Move.y);

            // Check if trying to climb down from the bottom segment - auto-detach
            if (climbDirection < 0 && IsAtBottomSegment())
            {
                Debug.Log("Trying to climb down from bottom segment - auto-detaching");
                _player.ToggleRope(false);
                return;
            }

            _currentRope.Climb(climbDirection);
            _timeSinceLastClimb = 0;
        }

        // Priority 5: Handle swinging
        if (Mathf.Abs(_player.FrameInputStruct.Move.x) > _stats.HorizontalDeadZoneThreshold)
        {
            float swingInput = Mathf.Clamp(_player.FrameInputStruct.Move.x, -1f, 1f);
            _currentRope.Swing(swingInput);
        }
    }

    private bool CheckIfBelowRope()
    {
        if (
            _currentRope == null
            || _currentRope.Segments == null
            || _currentRope.Segments.Length == 0
        )
            return false;

        // Get the player's collider
        var playerCollider = _player.GetComponent<CapsuleCollider2D>();
        if (playerCollider == null)
            return false;

        // Check if player is below the bottom-most rope segment
        GameObject bottomSegment = _currentRope.Segments[_currentRope.Segments.Length - 1];
        if (bottomSegment == null)
            return false;

        var bottomSegmentCollider = bottomSegment.GetComponent<Collider2D>();
        if (bottomSegmentCollider == null)
            return false;

        // Check if player's center is below the bottom of the last rope segment with a buffer
        float buffer = 0.5f; // Increased buffer for more reliable detachment
        return playerCollider.bounds.center.y <= (bottomSegmentCollider.bounds.min.y - buffer);
    }

    private bool IsAtBottomSegment()
    {
        if (
            _currentRope == null
            || _currentRope.Segments == null
            || _currentRope.Segments.Length == 0
        )
            return false;

        // Check if currently connected to the last (bottom-most) segment
        int bottomSegmentIndex = _currentRope.Segments.Length - 1;

        return _currentRope.ConnectedSegmentIndex >= bottomSegmentIndex;
    }

    private void CheckForRope()
    {
        if (_player.IsRopeClimbing)
            return;

        var hit = Physics2D.OverlapCircle(
            _player.handAnchor.position,
            _player.handAnchor.localScale.x,
            _stats.RopeSegmentLayer
        );
        if (hit != null && hit.transform.parent.TryGetComponent(out Rope rope))
        {
            // NEW: Don't re-attach to the same rope we just detached from if still on cooldown
            if (_detachCooldownTime > 0 && rope == _lastDetachedRope)
                return;

            Enter(rope);
        }
    }

    private void AttachToRope(Rope rope, Vector3 attachPosition)
    {
        if (rope == null || _playerRb == null)
            return;

        _currentRope = rope;
        _player.IsRopeClimbing = true;

        int closestSegment = rope.GetClosestSegmentIndex(attachPosition);
        rope.Attach(_playerRb, closestSegment);
    }

    private void ExecuteRopeJump()
    {
        if (_currentRope == null)
            return;

        // Calculate jump velocity based on player input
        Vector2 jumpDirection = new Vector2(
            _player.FrameInputStruct.Move.x * _stats.RopeJumpPower.x,
            _stats.RopeJumpPower.y
        );

        // Use the centralized jump method
        _player.PerformJump(jumpDirection);

        // Properly exit the rope state
        Exit();
    }
}
