using UnityEngine;

/// <summary>
/// Handles dash ability logic.
/// Extracted from PlayerController to follow single responsibility principle.
/// </summary>
public class DashAbility : IAbility
{
    private readonly DashStats _stats;

    private bool _isDashing;
    private float _dashTimeLeft;
    private Vector2 _dashDirection;
    private float _dashTraveled;
    private bool _dashToConsume;

    public bool IsActive => _isDashing;

    public DashAbility(DashStats stats)
    {
        _stats = stats;
    }

    public bool CanActivate(IPlayerContext context, FrameInput input)
    {
        return !_isDashing && !context.IsSwimming && !context.IsRopeClimbing;
    }

    public bool TryActivate(IPlayerContext context, FrameInput input)
    {
        if (!input.DashDown && !_dashToConsume)
            return false;

        if (!CanActivate(context, input))
            return false;

        StartDash(input, context);
        return true;
    }

    public void Update(float deltaTime)
    {
        // Dash logic is handled in FixedUpdate
    }

    public void FixedUpdate(float fixedDeltaTime)
    {
        if (!_isDashing)
            return;

        _dashTimeLeft -= fixedDeltaTime;
        _dashTraveled += _stats.DashPower * fixedDeltaTime;

        if (_dashTimeLeft <= 0 || _dashTraveled >= _stats.DashDistance)
        {
            Deactivate();
        }
    }

    public void Deactivate()
    {
        _isDashing = false;
        _dashTimeLeft = 0;
        _dashTraveled = 0;
    }

    /// <summary>
    /// Set dash input for consumption
    /// </summary>
    public void SetDashInput(bool dashPressed)
    {
        if (dashPressed)
        {
            _dashToConsume = true;
        }
    }

    /// <summary>
    /// Get the current dash velocity
    /// </summary>
    public Vector2 GetDashVelocity()
    {
        return _isDashing ? _dashDirection * _stats.DashPower : Vector2.zero;
    }

    private void StartDash(FrameInput input, IPlayerContext context)
    {
        _isDashing = true;
        _dashTimeLeft = _stats.DashDuration;
        _dashTraveled = 0f;

        // Determine dash direction
        var direction =
            input.Move != Vector2.zero ? input.Move.normalized : new Vector2(context.LastFacing, 0);

        _dashDirection = direction;
        _dashToConsume = false;
    }
}
