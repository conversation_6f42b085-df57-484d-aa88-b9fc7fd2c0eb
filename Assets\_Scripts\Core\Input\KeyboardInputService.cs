using UnityEngine;

/// <summary>
/// Keyboard/mouse implementation of input service.
/// Extracted from PlayerController to follow single responsibility principle.
/// </summary>
public class KeyboardInputService : MonoBehaviour, IInputService
{
    [SerializeField]
    private InputStats _inputStats;

    private FrameInput _currentInput;
    private float _lastFacing = 1f;
    private bool _jumpToConsume;
    private bool _dashToConsume;

    public FrameInput CurrentInput => _currentInput;
    public float LastFacing => _lastFacing;

    /// <summary>
    /// Sets the input stats configuration. Called by GameBootstrap.
    /// </summary>
    public void SetInputStats(InputStats inputStats)
    {
        _inputStats = inputStats;
    }

    private void Awake()
    {
        if (_inputStats == null)
        {
            Debug.LogError("InputStats not assigned to KeyboardInputService", this);
        }
    }

    public void UpdateInput()
    {
        GatherRawInput();
        ProcessInput();
    }

    private void GatherRawInput()
    {
        _currentInput = new FrameInput
        {
            JumpDown = _jumpToConsume || Input.GetButtonDown("Jump"),
            JumpHeld = Input.GetButton("Jump"),
            Move = new Vector2(Input.GetAxisRaw("Horizontal"), Input.GetAxisRaw("Vertical")),
            DashDown = _dashToConsume || Input.GetKeyDown(KeyCode.C),
            CrouchHeld = Input.GetKey(KeyCode.X),
            GlideHeld = Input.GetKey(KeyCode.G),
            InteractDown = Input.GetKeyDown(KeyCode.E),
        };

        // Track jump and dash inputs for consumption
        if (Input.GetButtonDown("Jump"))
            _jumpToConsume = true;
        if (Input.GetKeyDown(KeyCode.C))
            _dashToConsume = true;
    }

    private void ProcessInput()
    {
        if (_inputStats == null)
            return;

        // Apply input snapping if enabled
        if (_inputStats.SnapInput)
        {
            _currentInput.Move.x =
                Mathf.Abs(_currentInput.Move.x) < _inputStats.HorizontalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_currentInput.Move.x);
            _currentInput.Move.y =
                Mathf.Abs(_currentInput.Move.y) < _inputStats.VerticalDeadZoneThreshold
                    ? 0
                    : Mathf.Sign(_currentInput.Move.y);
        }

        // Track facing direction
        if (_currentInput.Move.x != 0)
            _lastFacing = _currentInput.Move.x;
    }

    public void ConsumeJump()
    {
        _jumpToConsume = false;
        var input = _currentInput;
        input.JumpDown = false;
        _currentInput = input;
    }

    public void ConsumeDash()
    {
        _dashToConsume = false;
        var input = _currentInput;
        input.DashDown = false;
        _currentInput = input;
    }
}
