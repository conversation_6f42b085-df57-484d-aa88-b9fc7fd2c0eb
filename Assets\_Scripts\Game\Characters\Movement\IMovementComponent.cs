using UnityEngine;

/// <summary>
/// Interface for movement components.
/// Each movement type (ground, air, swimming, etc.) implements this interface.
/// </summary>
public interface IMovementComponent
{
    /// <summary>
    /// Calculate the velocity for this movement type
    /// </summary>
    Vector2 CalculateVelocity(Vector2 currentVelocity, FrameInput input, float deltaTime);

    /// <summary>
    /// Check if this movement component can be active given current conditions
    /// </summary>
    bool CanBeActive(IPlayerContext context);

    /// <summary>
    /// Called when this movement component becomes active
    /// </summary>
    void OnActivated(IPlayerContext context);

    /// <summary>
    /// Called when this movement component becomes inactive
    /// </summary>
    void OnDeactivated(IPlayerContext context);
}
